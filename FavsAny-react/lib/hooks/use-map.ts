"use client"

import { useState, useCallback } from "react"
import type { Marker } from "@/lib/types"

// Mock data for demonstration
const mockMarkers: Marker[] = [
  {
    id: "1",
    name: "星巴克咖啡",
    title: "星巴克咖啡",
    address: "南京西路店",
    location: "南京西路店",
    lat: 31.2304,
    lng: 121.4737,
    iconId: "coffee",
    color: "bg-amber-500",
    category: "咖啡馆",
    description: "工作时经常去的咖啡店，环境很好，适合办公",
    image: "/placeholder.svg?height=200&width=300",
    tags: ["工作", "咖啡"],
    isPublic: false,
    date: "2024-01-15",
  },
  {
    id: "2",
    name: "南京路步行街",
    title: "南京路步行街",
    address: "黄浦区南京东路",
    location: "黄浦区",
    lat: 31.2342,
    lng: 121.4692,
    iconId: "shopping",
    color: "bg-blue-500",
    category: "购物",
    description: "上海最繁华的商业街，购物的好去处",
    image: "/placeholder.svg?height=200&width=300",
    tags: ["购物", "旅游"],
    isPublic: true,
    date: "2024-01-10",
  },
]

export function useMap() {
  const [markers, setMarkers] = useState<Marker[]>(mockMarkers)

  const addMarker = useCallback((marker: Omit<Marker, "id">) => {
    const newMarker: Marker = {
      ...marker,
      id: Date.now().toString(),
    }
    setMarkers((prev) => [...prev, newMarker])
    return newMarker
  }, [])

  const updateMarker = useCallback((id: string, updates: Partial<Marker>) => {
    setMarkers((prev) => prev.map((marker) => (marker.id === id ? { ...marker, ...updates } : marker)))
  }, [])

  const deleteMarker = useCallback(async (id: string) => {
    setMarkers((prev) => prev.filter((marker) => marker.id !== id))
  }, [])

  const getMarkerById = useCallback(
    (id: string) => {
      return markers.find((marker) => marker.id === id)
    },
    [markers],
  )

  return {
    markers,
    addMarker,
    updateMarker,
    deleteMarker,
    getMarkerById,
  }
}
