import type React from "react"
export interface Marker {
  id: string
  name: string
  address: string
  lat: number
  lng: number
  iconId?: string
  color?: string
  category?: string
  description?: string
  image?: string
  tags?: string[]
  isPublic?: boolean
  date?: string
  title?: string
  location?: string
  icon?: string | React.ComponentType<any>
}

export interface IconItem {
  id: string
  name: string
  icon: React.ComponentType<any>
  color: string
  keywords: string[]
}

export interface IconCategory {
  id: string
  name: string
  icons: IconItem[]
}
