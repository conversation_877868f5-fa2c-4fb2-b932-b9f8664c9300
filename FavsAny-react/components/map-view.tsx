"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, SlidersHorizontal, User } from "lucide-react"
import { getIconById } from "@/components/icons/icon-library"
import { BottomTabBar } from "@/components/bottom-tab-bar"
import { AmapContainer } from "@/components/amap/amap-container"

interface MapViewProps {
  onLongPress: (position: { lat: number; lng: number }) => void
  onMarkerClick: (marker: any) => void
  onFavoritesClick: () => void
  onSettingsClick: () => void
}

const mockMarkers = [
  {
    id: 1,
    lat: 31.2304,
    lng: 121.4737,
    title: "星巴克咖啡",
    category: "咖啡馆",
    iconId: "coffee",
    isPublic: false,
  },
  {
    id: 2,
    lat: 31.2404,
    lng: 121.4837,
    title: "南京路步行街",
    category: "购物",
    iconId: "shopping",
    isPublic: true,
  },
  {
    id: 3,
    lat: 31.2204,
    lng: 121.4637,
    title: "外滩观景点",
    category: "旅行",
    iconId: "photo",
    isPublic: true,
  },
  {
    id: 4,
    lat: 31.2504,
    lng: 121.4937,
    title: "健身房",
    category: "运动",
    iconId: "gym",
    isPublic: false,
  },
  {
    id: 5,
    lat: 31.2104,
    lng: 121.4537,
    title: "音乐厅",
    category: "娱乐",
    iconId: "music",
    isPublic: true,
  },
]

const categories = [
  { name: "全部", iconId: "bookmark", count: 12 },
  { name: "餐饮", iconId: "coffee", count: 3 },
  { name: "购物", iconId: "shopping", count: 2 },
  { name: "旅行", iconId: "photo", count: 2 },
  { name: "运动", iconId: "gym", count: 1 },
  { name: "娱乐", iconId: "music", count: 4 },
]

export function MapView({ onLongPress, onMarkerClick, onFavoritesClick, onSettingsClick }: MapViewProps) {
  const [selectedCategory, setSelectedCategory] = useState("全部")
  const [searchQuery, setSearchQuery] = useState("")
  const [userLocation, setUserLocation] = useState("上海, 中国")
  const [showFilters, setShowFilters] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const filteredMarkers = mockMarkers.filter(
    (marker) => selectedCategory === "全部" || marker.category === selectedCategory.replace("餐饮", "咖啡馆"),
  )

  if (!mounted) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 relative bg-gray-50">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-white/95 backdrop-blur-xl">
        <div className="px-6 pt-4 pb-3">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-base font-medium text-gray-900">{userLocation}</span>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                className="w-10 h-10 rounded-full bg-gray-100/80 hover:bg-gray-200/80"
                onClick={onSettingsClick}
              >
                <User className="w-5 h-5 text-gray-700" />
              </Button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex gap-3 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <Input
                placeholder="搜索收藏地点..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 h-12 bg-gray-100/80 border-0 rounded-2xl text-base placeholder:text-gray-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20"
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              className={`w-12 h-12 rounded-2xl border-0 transition-all ${
                showFilters
                  ? "bg-blue-500 text-white hover:bg-blue-600 shadow-lg shadow-blue-500/25"
                  : "bg-gray-100/80 hover:bg-gray-200/80"
              }`}
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal className="w-5 h-5" />
            </Button>
          </div>

          {/* Category Filter - 只在点击筛选按钮时显示 */}
          {showFilters && (
            <div className="flex gap-3 overflow-x-auto pb-2 animate-in slide-in-from-top-2 duration-200">
              {categories.map((category) => {
                const categoryIcon = getIconById(category.iconId)
                return (
                  <div
                    key={category.name}
                    className={`flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap cursor-pointer transition-all ${
                      selectedCategory === category.name
                        ? "bg-blue-500 text-white shadow-lg shadow-blue-500/25"
                        : "bg-white/80 text-gray-700 hover:bg-white"
                    }`}
                    onClick={() => setSelectedCategory(category.name)}
                  >
                    {categoryIcon && (
                      <div
                        className={`w-4 h-4 ${selectedCategory === category.name ? "bg-white/20" : categoryIcon.color} rounded-lg flex items-center justify-center`}
                      >
                        <categoryIcon.icon
                          className={`w-2.5 h-2.5 ${selectedCategory === category.name ? "text-white" : "text-white"}`}
                        />
                      </div>
                    )}
                    <span className="text-sm font-medium">{category.name}</span>
                    <span
                      className={`text-xs px-1.5 py-0.5 rounded-full ${
                        selectedCategory === category.name ? "bg-white/20 text-white" : "bg-gray-100 text-gray-500"
                      }`}
                    >
                      {category.count}
                    </span>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Map Area */}
      <AmapContainer
        onLongPress={onLongPress}
        onMarkerClick={onMarkerClick}
        markers={filteredMarkers}
        className="w-full h-full"
        style={{ paddingTop: showFilters ? "280px" : "220px", paddingBottom: "100px" }}
      />

      {/* Add Marker Hint */}
      <div className="absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black/80 backdrop-blur-sm text-white px-6 py-3 rounded-2xl text-sm font-medium pointer-events-none z-10">
        长按地图添加收藏点
      </div>

      {/* Bottom Tab Bar */}
      <BottomTabBar
        activeTab="map"
        onTabChange={(tab) => {
          if (tab === "favorites") {
            onFavoritesClick()
          }
        }}
      />
    </div>
  )
}