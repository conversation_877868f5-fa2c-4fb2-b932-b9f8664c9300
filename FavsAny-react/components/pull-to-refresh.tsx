"use client"

import type React from "react"

import { useState, useRef, useCallback } from "react"
import { useTouchGestures } from "@/hooks/use-touch-gestures"
import { useMobileFeatures } from "@/hooks/use-mobile-features"
import { RefreshCw } from "lucide-react"

interface PullToRefreshProps {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  threshold?: number
}

export function PullToRefresh({ children, onRefresh, threshold = 80 }: PullToRefreshProps) {
  const [pullDistance, setPullDistance] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [canRefresh, setCanRefresh] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const { vibrate } = useMobileFeatures()

  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return

    setIsRefreshing(true)
    vibrate(50)

    try {
      await onRefresh()
    } finally {
      setIsRefreshing(false)
      setPullDistance(0)
      setCanRefresh(false)
    }
  }, [onRefresh, isRefreshing, vibrate])

  const touchGestures = useTouchGestures({
    onSwipeDown: (e) => {
      const container = containerRef.current
      if (!container || container.scrollTop > 0) return

      const touch = e.touches[0] || e.changedTouches[0]
      const startY = touch.clientY

      const handleMove = (moveEvent: TouchEvent) => {
        const currentTouch = moveEvent.touches[0]
        const deltaY = Math.max(0, currentTouch.clientY - startY)
        const distance = Math.min(deltaY * 0.5, threshold * 1.5)

        setPullDistance(distance)

        if (distance >= threshold && !canRefresh) {
          setCanRefresh(true)
          vibrate(30)
        } else if (distance < threshold && canRefresh) {
          setCanRefresh(false)
        }
      }

      const handleEnd = () => {
        if (canRefresh) {
          handleRefresh()
        } else {
          setPullDistance(0)
          setCanRefresh(false)
        }

        document.removeEventListener("touchmove", handleMove)
        document.removeEventListener("touchend", handleEnd)
      }

      document.addEventListener("touchmove", handleMove)
      document.addEventListener("touchend", handleEnd)
    },
  })

  return (
    <div className="relative overflow-hidden" ref={containerRef} {...touchGestures}>
      {/* Pull to Refresh Indicator */}
      <div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-gray-50 transition-transform duration-200"
        style={{
          height: `${Math.max(pullDistance, 0)}px`,
          transform: `translateY(-${Math.max(0, threshold - pullDistance)}px)`,
        }}
      >
        {pullDistance > 0 && (
          <div className="flex items-center gap-2 text-gray-600">
            <RefreshCw
              className={`w-5 h-5 ${isRefreshing ? "animate-spin" : ""} ${canRefresh ? "text-blue-500" : ""}`}
            />
            <span className="text-sm">{isRefreshing ? "刷新中..." : canRefresh ? "松开刷新" : "下拉刷新"}</span>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="transition-transform duration-200" style={{ transform: `translateY(${pullDistance}px)` }}>
        {children}
      </div>
    </div>
  )
}
