"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Search, SlidersHorizontal, MapPin, Calendar, Eye, EyeOff } from "lucide-react"
import { SwipeableListItem } from "@/components/swipeable-list-item"
import { PullToRefresh } from "@/components/pull-to-refresh"
import { IconDisplay } from "@/components/icons/icon-display"
import { getIconById } from "@/components/icons/icon-library"
import { BottomTabBar } from "@/components/bottom-tab-bar"

interface CollectionsListProps {
  onBackClick: () => void
  onMarkerClick: (marker: any) => void
}

const mockCollections = [
  {
    id: 1,
    title: "星巴克咖啡",
    description: "工作时经常去的咖啡店，环境很好，适合办公",
    category: "咖啡馆",
    location: "南京西路店",
    date: "2024-01-15",
    image: "/placeholder.svg?height=120&width=120",
    isPublic: false,
    tags: ["工作", "咖啡"],
    iconId: "coffee",
  },
  {
    id: 2,
    title: "南京路步行街",
    description: "上海最繁华的商业街，购物的好去处",
    category: "购物",
    location: "黄浦区",
    date: "2024-01-10",
    image: "/placeholder.svg?height=120&width=120",
    isPublic: true,
    tags: ["购物", "旅游"],
    iconId: "shopping",
  },
  {
    id: 3,
    title: "外滩观景点",
    description: "拍照打卡的绝佳位置，夜景特别美",
    category: "旅行",
    location: "黄浦江边",
    date: "2024-01-08",
    image: "/placeholder.svg?height=120&width=120",
    isPublic: true,
    tags: ["摄影", "夜景"],
    iconId: "photo",
  },
  {
    id: 4,
    title: "健身房",
    description: "设备齐全的健身房，教练很专业",
    category: "运动",
    location: "体育中心",
    date: "2024-01-12",
    image: "/placeholder.svg?height=120&width=120",
    isPublic: false,
    tags: ["健身", "运动"],
    iconId: "gym",
  },
  {
    id: 5,
    title: "音乐厅",
    description: "音响效果很棒的音乐厅，经常有演出",
    category: "娱乐",
    location: "文化中心",
    date: "2024-01-05",
    image: "/placeholder.svg?height=120&width=120",
    isPublic: true,
    tags: ["音乐", "演出"],
    iconId: "music",
  },
]

export function CollectionsList({ onBackClick, onMarkerClick }: CollectionsListProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("全部")

  const categories = ["全部", "餐饮", "购物", "旅行", "运动", "娱乐"]

  const filteredCollections = mockCollections.filter((item) => {
    const matchesSearch =
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory =
      selectedCategory === "全部" ||
      (selectedCategory === "餐饮" && item.category === "咖啡馆") ||
      item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleRefresh = async () => {
    await new Promise((resolve) => setTimeout(resolve, 1000))
    console.log("Data refreshed")
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-xl p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="icon"
            className="w-10 h-10 rounded-2xl bg-gray-100 hover:bg-gray-200"
            onClick={onBackClick}
          >
            <ArrowLeft className="w-5 h-5 text-gray-700" />
          </Button>
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900">我的收藏</h1>
            <p className="text-sm text-gray-500 mt-1">{filteredCollections.length} 个精选地点</p>
          </div>
        </div>

        {/* Search */}
        <div className="flex gap-3 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              placeholder="搜索收藏..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 h-12 bg-gray-100 border-0 rounded-2xl text-base placeholder:text-gray-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20"
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            className="w-12 h-12 rounded-2xl bg-gray-100 border-0 hover:bg-gray-200"
          >
            <SlidersHorizontal className="w-5 h-5 text-gray-700" />
          </Button>
        </div>

        {/* Categories */}
        <div className="flex gap-3 overflow-x-auto pb-2">
          {categories.map((category) => (
            <div
              key={category}
              className={`px-4 py-2 rounded-full whitespace-nowrap cursor-pointer transition-all text-sm font-medium ${
                selectedCategory === category
                  ? "bg-blue-500 text-white shadow-lg shadow-blue-500/25"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </div>
          ))}
        </div>
      </div>

      {/* Collections List */}
      <PullToRefresh onRefresh={handleRefresh}>
        <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4 pb-24">
          {filteredCollections.map((item) => {
            const itemIcon = getIconById(item.iconId)
            if (!itemIcon) return null

            return (
              <SwipeableListItem
                key={item.id}
                onEdit={() => console.log("Edit", item.id)}
                onDelete={() => console.log("Delete", item.id)}
                onShare={() => onMarkerClick({ ...item, icon: itemIcon })}
                onToggleVisibility={() => console.log("Toggle visibility", item.id)}
                isPublic={item.isPublic}
              >
                <div className="bg-white rounded-3xl p-6 shadow-sm hover:shadow-md transition-all cursor-pointer">
                  <div className="flex gap-4">
                    {/* Icon */}
                    <div className="relative">
                      <IconDisplay icon={itemIcon} size="xl" />
                      <div
                        className={`absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center shadow-sm ${
                          item.isPublic ? "bg-green-500" : "bg-gray-400"
                        }`}
                      >
                        {item.isPublic ? (
                          <Eye className="w-3 h-3 text-white" />
                        ) : (
                          <EyeOff className="w-3 h-3 text-white" />
                        )}
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">{item.title}</h3>
                      <p className="text-gray-600 mb-4 line-clamp-2 leading-relaxed">{item.description}</p>

                      <div className="flex items-center gap-6 text-sm text-gray-500 mb-4">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span>{item.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          <span>{item.date}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        {item.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </SwipeableListItem>
            )
          })}

          {filteredCollections.length === 0 && (
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-gray-100 rounded-3xl flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏</h3>
              <p className="text-gray-500">在地图上长按添加你的第一个收藏点</p>
            </div>
          )}
        </div>
      </PullToRefresh>

      {/* Bottom Tab Bar */}
      <BottomTabBar
        activeTab="favorites"
        onTabChange={(tab) => {
          if (tab === "map") onBackClick()
        }}
      />
    </div>
  )
}
