"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Home, Heart } from "lucide-react"

interface BottomTabBarProps {
  activeTab: "map" | "favorites"
  onTabChange: (tab: "map" | "favorites") => void
}

export function BottomTabBar({ activeTab, onTabChange }: BottomTabBarProps) {
  const tabs = [
    {
      id: "map" as const,
      icon: Home,
      label: "首页",
    },
    {
      id: "favorites" as const,
      icon: Heart,
      label: "收藏",
    },
  ]

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* 背景模糊层 */}
      <div className="absolute inset-0 bg-white/80 backdrop-blur-xl" />

      {/* 主容器 */}
      <div className="relative px-6 py-4">
        <div className="bg-white rounded-3xl shadow-lg shadow-black/5 border border-gray-100/50 px-2 py-2">
          <div className="flex items-center justify-between">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              const isActive = activeTab === tab.id

              return (
                <Button
                  key={tab.id}
                  variant="ghost"
                  className={`flex-1 flex items-center justify-center h-14 rounded-2xl transition-all duration-200 ${
                    isActive ? "bg-blue-500 hover:bg-blue-600 shadow-lg shadow-blue-500/25" : "hover:bg-gray-50"
                  }`}
                  onClick={() => onTabChange(tab.id)}
                >
                  <IconComponent className={`w-6 h-6 transition-colors ${isActive ? "text-white" : "text-gray-700"}`} />
                </Button>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
