"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { MapPin, Upload, X, Mic, MicOff, Camera } from "lucide-react"
import { useMobileFeatures } from "@/hooks/use-mobile-features"
import { IconSelector } from "@/components/icons/icon-selector"
import { IconDisplay } from "@/components/icons/icon-display"
import { getIconById, type IconItem } from "@/components/icons/icon-library"

interface AddMarkerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  position: { lat: number; lng: number } | null
}

export function AddMarkerDialog({ open, onOpenChange, position }: <PERSON>d<PERSON>arkerDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [selectedIcon, setSelectedIcon] = useState<IconItem | undefined>(getIconById("coffee"))
  const [customTags, setCustomTags] = useState("")
  const [isPublic, setIsPublic] = useState(false)
  const [images, setImages] = useState<string[]>([])
  const [showIconSelector, setShowIconSelector] = useState(false)

  const { takePhoto, startVoiceRecording, vibrate, isRecording } = useMobileFeatures()
  const [voiceNotes, setVoiceNotes] = useState<Blob[]>([])
  const [isRecordingVoice, setIsRecordingVoice] = useState(false)

  const handleSave = () => {
    console.log({
      title,
      description,
      icon: selectedIcon,
      tags: customTags.split(" ").filter((tag) => tag.trim()),
      isPublic,
      position,
      images,
      voiceNotes,
    })

    // 重置表单
    setTitle("")
    setDescription("")
    setSelectedIcon(getIconById("coffee"))
    setCustomTags("")
    setIsPublic(false)
    setImages([])
    setVoiceNotes([])
    onOpenChange(false)
  }

  const handleImageUpload = () => {
    setImages([...images, "/placeholder.svg?height=100&width=100"])
  }

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index))
  }

  const handleVoiceRecording = async () => {
    if (isRecordingVoice) {
      setIsRecordingVoice(false)
      vibrate(30)
    } else {
      try {
        vibrate(50)
        setIsRecordingVoice(true)
        const voiceBlob = await startVoiceRecording()
        setVoiceNotes([...voiceNotes, voiceBlob])
        setIsRecordingVoice(false)
      } catch (error) {
        console.error("Voice recording failed:", error)
        setIsRecordingVoice(false)
      }
    }
  }

  const handleTakePhoto = async () => {
    try {
      vibrate(30)
      const photoUrl = await takePhoto()
      setImages([...images, photoUrl])
    } catch (error) {
      console.error("Photo capture failed:", error)
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto rounded-3xl">
          <DialogHeader className="pb-6">
            <DialogTitle className="text-xl font-semibold text-center">添加收藏点</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* 位置信息 */}
            {position && (
              <div className="bg-blue-50 p-4 rounded-2xl">
                <div className="flex items-center gap-3 text-blue-700">
                  <div className="w-8 h-8 bg-blue-500 rounded-xl flex items-center justify-center">
                    <MapPin className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">当前位置</p>
                    <p className="text-sm opacity-80">
                      {position.lat.toFixed(4)}, {position.lng.toFixed(4)}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* 标题输入 */}
            <div className="space-y-3">
              <Label htmlFor="title" className="text-base font-medium text-gray-900">
                名称 *
              </Label>
              <Input
                id="title"
                placeholder="给这个地点起个名字..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="h-12 rounded-2xl border-gray-200 text-base placeholder:text-gray-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>

            {/* 图标选择 */}
            <div className="space-y-3">
              <Label className="text-base font-medium text-gray-900">图标</Label>
              <Button
                variant="outline"
                className="w-full h-16 rounded-2xl border-2 border-dashed border-gray-300 hover:border-gray-400 bg-transparent justify-start gap-4"
                onClick={() => setShowIconSelector(true)}
              >
                {selectedIcon ? (
                  <IconDisplay icon={selectedIcon} size="lg" showName />
                ) : (
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-2xl flex items-center justify-center">
                      <MapPin className="w-5 h-5 text-gray-400" />
                    </div>
                    <span className="text-gray-500">选择图标</span>
                  </div>
                )}
              </Button>
            </div>

            {/* 描述输入 */}
            <div className="space-y-3">
              <Label htmlFor="description" className="text-base font-medium text-gray-900">
                描述
              </Label>
              <Textarea
                id="description"
                placeholder="记录一些关于这个地点的想法..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                className="rounded-2xl border-gray-200 text-base placeholder:text-gray-500 focus:ring-2 focus:ring-blue-500/20 resize-none"
              />
            </div>

            {/* 媒体文件上传 */}
            <div className="space-y-3">
              <Label className="text-base font-medium text-gray-900">媒体文件</Label>
              <div className="grid grid-cols-4 gap-3">
                {images.map((image, index) => (
                  <div key={index} className="relative aspect-square">
                    <img src={image || "/placeholder.svg"} alt="" className="w-full h-full object-cover rounded-2xl" />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute -top-2 -right-2 w-6 h-6 rounded-full shadow-sm"
                      onClick={() => removeImage(index)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                ))}

                {voiceNotes.map((_, index) => (
                  <div
                    key={`voice-${index}`}
                    className="relative aspect-square bg-blue-100 rounded-2xl flex items-center justify-center"
                  >
                    <Mic className="w-6 h-6 text-blue-600" />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute -top-2 -right-2 w-6 h-6 rounded-full shadow-sm"
                      onClick={() => setVoiceNotes(voiceNotes.filter((_, i) => i !== index))}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                ))}

                <Button
                  variant="outline"
                  className="aspect-square flex flex-col items-center justify-center gap-2 rounded-2xl border-2 border-dashed border-gray-300 hover:border-gray-400 bg-transparent"
                  onClick={handleImageUpload}
                >
                  <Upload className="w-5 h-5 text-gray-500" />
                  <span className="text-xs text-gray-500">相册</span>
                </Button>

                <Button
                  variant="outline"
                  className="aspect-square flex flex-col items-center justify-center gap-2 rounded-2xl border-2 border-dashed border-gray-300 hover:border-gray-400 bg-transparent"
                  onClick={handleTakePhoto}
                >
                  <Camera className="w-5 h-5 text-gray-500" />
                  <span className="text-xs text-gray-500">拍照</span>
                </Button>

                <Button
                  variant={isRecordingVoice ? "destructive" : "outline"}
                  className="aspect-square flex flex-col items-center justify-center gap-2 rounded-2xl border-2 border-dashed border-gray-300 hover:border-gray-400 bg-transparent"
                  onClick={handleVoiceRecording}
                  disabled={isRecording}
                >
                  {isRecordingVoice ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5 text-gray-500" />}
                  <span className="text-xs text-gray-500">{isRecordingVoice ? "停止" : "录音"}</span>
                </Button>
              </div>

              {isRecordingVoice && (
                <div className="text-center text-sm text-red-600 animate-pulse bg-red-50 p-3 rounded-2xl">
                  正在录音... 点击停止按钮结束录制
                </div>
              )}
            </div>

            {/* 标签输入 */}
            <div className="space-y-3">
              <Label htmlFor="tags" className="text-base font-medium text-gray-900">
                标签
              </Label>
              <Input
                id="tags"
                placeholder="用空格分隔多个标签，如：工作 咖啡 安静"
                value={customTags}
                onChange={(e) => setCustomTags(e.target.value)}
                className="h-12 rounded-2xl border-gray-200 text-base placeholder:text-gray-500 focus:ring-2 focus:ring-blue-500/20"
              />
              {customTags && (
                <div className="flex gap-2 flex-wrap">
                  {customTags
                    .split(" ")
                    .filter((tag) => tag.trim())
                    .map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium"
                      >
                        #{tag}
                      </span>
                    ))}
                </div>
              )}
            </div>

            {/* 隐私设置 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl">
              <div>
                <div className="font-medium text-gray-900">公开分享</div>
                <div className="text-sm text-gray-500 mt-1">允许其他人查看这个收藏点</div>
              </div>
              <Switch checked={isPublic} onCheckedChange={setIsPublic} />
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                className="flex-1 h-12 rounded-2xl border-gray-200 bg-transparent hover:bg-gray-50"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button
                className="flex-1 h-12 rounded-2xl bg-blue-500 hover:bg-blue-600 shadow-lg shadow-blue-500/25"
                onClick={handleSave}
                disabled={!title.trim()}
              >
                保存
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Icon Selector Dialog */}
      <IconSelector
        open={showIconSelector}
        onOpenChange={setShowIconSelector}
        onSelect={setSelectedIcon}
        selectedIcon={selectedIcon}
      />
    </>
  )
}
