"use client"

import type React from "react"

import {
  Coffee,
  Utensils,
  Pizza,
  Wine,
  IceCream,
  Cake,
  Car,
  Bus,
  Plane,
  Train,
  Bike,
  MapPin,
  Home,
  Building,
  School,
  Hospital,
  BanknoteIcon as Bank,
  Hotel,
  Music,
  Camera,
  Gamepad2,
  Film,
  Headphones,
  Palette,
  TreesIcon as Tree,
  Mountain,
  Waves,
  Sun,
  Cloud,
  Flower,
  ShoppingBag,
  Store,
  Gift,
  CreditCard,
  Tag,
  Percent,
  Briefcase,
  Laptop,
  FileText,
  Calendar,
  Clock,
  Users,
  Heart,
  Activity,
  Dumbbell,
  Pill,
  Stethoscope,
  Shield,
  Star,
  Flag,
  Bookmark,
  Award,
  Trophy,
  Target,
} from "lucide-react"
import type { IconItem, IconCategory } from "@/lib/types"

// 图标库数据
export const iconCategories: IconCategory[] = [
  {
    id: "food",
    name: "餐饮",
    icons: [
      { id: "coffee", name: "咖啡", icon: Coffee, color: "bg-amber-500", keywords: ["咖啡", "饮品", "cafe"] },
      { id: "restaurant", name: "餐厅", icon: Utensils, color: "bg-orange-500", keywords: ["餐厅", "用餐", "食物"] },
      { id: "pizza", name: "披萨", icon: Pizza, color: "bg-red-500", keywords: ["披萨", "快餐", "意式"] },
      { id: "wine", name: "酒吧", icon: Wine, color: "bg-purple-500", keywords: ["酒吧", "酒类", "夜生活"] },
      { id: "icecream", name: "冰淇淋", icon: IceCream, color: "bg-pink-500", keywords: ["冰淇淋", "甜品", "冷饮"] },
      { id: "cake", name: "蛋糕", icon: Cake, color: "bg-yellow-500", keywords: ["蛋糕", "甜品", "烘焙"] },
    ],
  },
  {
    id: "transport",
    name: "交通",
    icons: [
      { id: "car", name: "汽车", icon: Car, color: "bg-gray-500", keywords: ["汽车", "停车", "驾驶"] },
      { id: "bus", name: "公交", icon: Bus, color: "bg-green-500", keywords: ["公交", "巴士", "公共交通"] },
      { id: "plane", name: "飞机", icon: Plane, color: "bg-blue-500", keywords: ["飞机", "机场", "航班"] },
      { id: "train", name: "火车", icon: Train, color: "bg-indigo-500", keywords: ["火车", "地铁", "铁路"] },
      { id: "bike", name: "自行车", icon: Bike, color: "bg-teal-500", keywords: ["自行车", "骑行", "单车"] },
      { id: "location", name: "位置", icon: MapPin, color: "bg-red-500", keywords: ["位置", "地点", "标记"] },
    ],
  },
  {
    id: "places",
    name: "地点",
    icons: [
      { id: "home", name: "家", icon: Home, color: "bg-blue-500", keywords: ["家", "住宅", "房屋"] },
      { id: "building", name: "建筑", icon: Building, color: "bg-gray-500", keywords: ["建筑", "办公楼", "大厦"] },
      { id: "school", name: "学校", icon: School, color: "bg-yellow-500", keywords: ["学校", "教育", "学习"] },
      { id: "hospital", name: "医院", icon: Hospital, color: "bg-red-500", keywords: ["医院", "医疗", "健康"] },
      { id: "bank", name: "银行", icon: Bank, color: "bg-green-500", keywords: ["银行", "金融", "ATM"] },
      { id: "hotel", name: "酒店", icon: Hotel, color: "bg-purple-500", keywords: ["酒店", "住宿", "旅行"] },
    ],
  },
  {
    id: "entertainment",
    name: "娱乐",
    icons: [
      { id: "music", name: "音乐", icon: Music, color: "bg-pink-500", keywords: ["音乐", "演出", "音响"] },
      { id: "photo", name: "摄影", icon: Camera, color: "bg-indigo-500", keywords: ["摄影", "拍照", "相机"] },
      { id: "game", name: "游戏", icon: Gamepad2, color: "bg-green-500", keywords: ["游戏", "娱乐", "电玩"] },
      { id: "movie", name: "电影", icon: Film, color: "bg-red-500", keywords: ["电影", "影院", "观影"] },
      { id: "headphones", name: "音响", icon: Headphones, color: "bg-gray-500", keywords: ["音响", "耳机", "音频"] },
      { id: "art", name: "艺术", icon: Palette, color: "bg-orange-500", keywords: ["艺术", "绘画", "创作"] },
    ],
  },
  {
    id: "nature",
    name: "自然",
    icons: [
      { id: "tree", name: "树木", icon: Tree, color: "bg-green-500", keywords: ["树木", "森林", "自然"] },
      { id: "mountain", name: "山峰", icon: Mountain, color: "bg-gray-500", keywords: ["山峰", "登山", "户外"] },
      { id: "water", name: "水域", icon: Waves, color: "bg-blue-500", keywords: ["水域", "海洋", "湖泊"] },
      { id: "sun", name: "阳光", icon: Sun, color: "bg-yellow-500", keywords: ["阳光", "天气", "晴天"] },
      { id: "cloud", name: "云朵", icon: Cloud, color: "bg-gray-400", keywords: ["云朵", "天气", "阴天"] },
      { id: "flower", name: "花朵", icon: Flower, color: "bg-pink-500", keywords: ["花朵", "植物", "花园"] },
    ],
  },
  {
    id: "shopping",
    name: "购物",
    icons: [
      { id: "shopping", name: "购物", icon: ShoppingBag, color: "bg-purple-500", keywords: ["购物", "商场", "买东西"] },
      { id: "store", name: "商店", icon: Store, color: "bg-blue-500", keywords: ["商店", "店铺", "零售"] },
      { id: "gift", name: "礼品", icon: Gift, color: "bg-red-500", keywords: ["礼品", "礼物", "赠品"] },
      { id: "card", name: "支付", icon: CreditCard, color: "bg-green-500", keywords: ["支付", "信用卡", "付款"] },
      { id: "tag", name: "标签", icon: Tag, color: "bg-orange-500", keywords: ["标签", "价格", "标记"] },
      { id: "discount", name: "折扣", icon: Percent, color: "bg-yellow-500", keywords: ["折扣", "优惠", "促销"] },
    ],
  },
  {
    id: "work",
    name: "工作",
    icons: [
      { id: "work", name: "工作", icon: Briefcase, color: "bg-gray-500", keywords: ["工作", "办公", "职业"] },
      { id: "laptop", name: "电脑", icon: Laptop, color: "bg-blue-500", keywords: ["电脑", "笔记本", "办公"] },
      { id: "document", name: "文档", icon: FileText, color: "bg-green-500", keywords: ["文档", "文件", "资料"] },
      { id: "calendar", name: "日历", icon: Calendar, color: "bg-red-500", keywords: ["日历", "日程", "时间"] },
      { id: "clock", name: "时钟", icon: Clock, color: "bg-orange-500", keywords: ["时钟", "时间", "计时"] },
      { id: "team", name: "团队", icon: Users, color: "bg-purple-500", keywords: ["团队", "人员", "合作"] },
    ],
  },
  {
    id: "health",
    name: "健康",
    icons: [
      { id: "heart", name: "心脏", icon: Heart, color: "bg-red-500", keywords: ["心脏", "健康", "爱心"] },
      { id: "activity", name: "活动", icon: Activity, color: "bg-green-500", keywords: ["活动", "运动", "健身"] },
      { id: "gym", name: "健身", icon: Dumbbell, color: "bg-blue-500", keywords: ["健身", "运动", "锻炼"] },
      { id: "medicine", name: "药物", icon: Pill, color: "bg-yellow-500", keywords: ["药物", "医疗", "治疗"] },
      { id: "medical", name: "医疗", icon: Stethoscope, color: "bg-teal-500", keywords: ["医疗", "诊断", "检查"] },
      { id: "safety", name: "安全", icon: Shield, color: "bg-indigo-500", keywords: ["安全", "保护", "防护"] },
    ],
  },
  {
    id: "other",
    name: "其他",
    icons: [
      { id: "star", name: "星星", icon: Star, color: "bg-yellow-500", keywords: ["星星", "收藏", "喜欢"] },
      { id: "flag", name: "旗帜", icon: Flag, color: "bg-red-500", keywords: ["旗帜", "标记", "重要"] },
      { id: "bookmark", name: "书签", icon: Bookmark, color: "bg-blue-500", keywords: ["书签", "收藏", "标记"] },
      { id: "award", name: "奖励", icon: Award, color: "bg-orange-500", keywords: ["奖励", "成就", "荣誉"] },
      { id: "trophy", name: "奖杯", icon: Trophy, color: "bg-yellow-600", keywords: ["奖杯", "胜利", "冠军"] },
      { id: "target", name: "目标", icon: Target, color: "bg-green-600", keywords: ["目标", "瞄准", "焦点"] },
    ],
  },
]

// 获取所有图标的扁平化数组
export const allIcons: IconItem[] = iconCategories.flatMap((category) => category.icons)

// 根据ID获取图标项
export function getIconById(id: string): IconItem | undefined {
  return allIcons.find((icon) => icon.id === id)
}

// 根据ID获取图标组件
export function getIconComponentById(id: string): React.ComponentType<any> | undefined {
  const iconItem = getIconById(id)
  return iconItem?.icon
}

// 搜索图标
export function searchIcons(query: string): IconItem[] {
  if (!query.trim()) return allIcons

  const lowerQuery = query.toLowerCase()
  return allIcons.filter(
    (icon) =>
      icon.name.toLowerCase().includes(lowerQuery) ||
      icon.keywords.some((keyword) => keyword.toLowerCase().includes(lowerQuery)),
  )
}

// 根据分类获取图标
export function getIconsByCategory(categoryId: string): IconItem[] {
  const category = iconCategories.find((cat) => cat.id === categoryId)
  return category?.icons || []
}
