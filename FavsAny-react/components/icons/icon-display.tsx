"use client"

import type { IconItem } from "./icon-library"

interface IconDisplayProps {
  icon: IconItem
  size?: "sm" | "md" | "lg" | "xl"
  showName?: boolean
  className?: string
}

const sizeClasses = {
  sm: "w-6 h-6",
  md: "w-8 h-8",
  lg: "w-10 h-10",
  xl: "w-12 h-12",
}

const iconSizes = {
  sm: "w-3 h-3",
  md: "w-4 h-4",
  lg: "w-5 h-5",
  xl: "w-6 h-6",
}

export function IconDisplay({ icon, size = "md", showName = false, className = "" }: IconDisplayProps) {
  const IconComponent = icon.icon

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`${sizeClasses[size]} ${icon.color} rounded-2xl flex items-center justify-center shadow-sm`}>
        <IconComponent className={`${iconSizes[size]} text-white`} />
      </div>
      {showName && <span className="text-sm font-medium text-gray-700">{icon.name}</span>}
    </div>
  )
}
