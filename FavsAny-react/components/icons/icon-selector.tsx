"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, X } from "lucide-react"
import { iconCategories, searchIcons, type IconItem } from "./icon-library"

interface IconSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSelect: (icon: IconItem) => void
  selectedIcon?: IconItem
}

export function IconSelector({ open, onOpenChange, onSelect, selectedIcon }: IconSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("dining")

  const filteredIcons = searchQuery
    ? searchIcons(searchQuery)
    : iconCategories.find((cat) => cat.id === selectedCategory)?.icons || []

  const handleSelect = (icon: IconItem) => {
    onSelect(icon)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[80vh] rounded-3xl">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold text-center">选择图标</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              placeholder="搜索图标..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 h-12 rounded-2xl border-gray-200 text-base placeholder:text-gray-500 focus:ring-2 focus:ring-blue-500/20"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full"
                onClick={() => setSearchQuery("")}
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Categories */}
          {!searchQuery && (
            <div className="flex gap-2 overflow-x-auto pb-2">
              {iconCategories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  className={`whitespace-nowrap rounded-full text-sm ${
                    selectedCategory === category.id
                      ? "bg-blue-500 text-white shadow-lg shadow-blue-500/25"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name}
                </Button>
              ))}
            </div>
          )}

          {/* Icons Grid */}
          <ScrollArea className="h-80">
            <div className="grid grid-cols-4 gap-3 p-1">
              {filteredIcons.map((icon) => {
                const IconComponent = icon.icon
                const isSelected = selectedIcon?.id === icon.id

                return (
                  <Button
                    key={icon.id}
                    variant="ghost"
                    className={`aspect-square flex flex-col items-center justify-center gap-2 p-3 rounded-2xl transition-all ${
                      isSelected
                        ? "bg-blue-50 border-2 border-blue-200 shadow-sm"
                        : "hover:bg-gray-50 border-2 border-transparent"
                    }`}
                    onClick={() => handleSelect(icon)}
                  >
                    <div className={`w-10 h-10 ${icon.color} rounded-2xl flex items-center justify-center shadow-sm`}>
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xs text-gray-600 text-center leading-tight">{icon.name}</span>
                  </Button>
                )
              })}
            </div>

            {filteredIcons.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>未找到相关图标</p>
              </div>
            )}
          </ScrollArea>

          {/* Selected Icon Display */}
          {selectedIcon && (
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-2xl">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 ${selectedIcon.color} rounded-2xl flex items-center justify-center`}>
                  <selectedIcon.icon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{selectedIcon.name}</p>
                  <p className="text-sm text-gray-500">已选择</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
