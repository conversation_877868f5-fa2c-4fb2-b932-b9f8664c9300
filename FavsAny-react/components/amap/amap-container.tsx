"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Navigation } from "lucide-react"
import { IconDisplay } from "@/components/icons/icon-display"
import { getIconById } from "@/components/icons/icon-library"

declare global {
  interface Window {
    AMap: any
    AMapLoader: any
  }
}

interface AmapContainerProps {
  onLongPress: (position: { lat: number; lng: number }) => void
  onMarkerClick: (marker: any) => void
  markers?: any[]
  className?: string
  style?: React.CSSProperties
}

export function AmapContainer({ onLongPress, onMarkerClick, markers = [], className, style }: AmapContainerProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const markersRef = useRef<any[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLocationLoading, setIsLocationLoading] = useState(false)

  // 加载高德地图
  useEffect(() => {
    const loadAMap = async () => {
      try {
        // 如果已经加载过，直接返回
        if (window.AMap) {
          setIsLoaded(true)
          return
        }

        // 动态加载高德地图 JS API
        const script = document.createElement('script')
        script.src = `https://webapi.amap.com/maps?v=2.0&key=e02aa654323adbcfdf59a3ae268f586d&plugin=AMap.Scale,AMap.ToolBar,AMap.ControlBar,AMap.Geolocation`
        script.async = true
        
        script.onload = () => {
          setIsLoaded(true)
        }
        
        script.onerror = () => {
          setError('地图加载失败，请检查网络连接')
        }
        
        document.head.appendChild(script)
      } catch (err) {
        setError('地图初始化失败')
        console.error('AMap loading error:', err)
      }
    }

    loadAMap()
  }, [])

  // 初始化地图
  useEffect(() => {
    if (!isLoaded || !mapRef.current || mapInstanceRef.current) return

    try {
      // 创建地图实例
      const map = new window.AMap.Map(mapRef.current, {
        zoom: 13,
        center: [121.4737, 31.2304], // 上海中心
        mapStyle: 'amap://styles/normal',
        viewMode: '2D',
        features: ['bg', 'road', 'building', 'point'],
        showLabel: true,
      })

      // 添加控件
      map.addControl(new window.AMap.Scale())
      map.addControl(new window.AMap.ToolBar({
        locate: false,
        noIpLocate: false,
        locationMarker: false,
        useNative: false
      }))

      // 桌面端和移动端统一使用长按事件
      let pressTimer: NodeJS.Timeout | null = null
      let isLongPress = false

      // 鼠标按下事件（桌面端）
      map.on('mousedown', (e: any) => {
        isLongPress = false
        pressTimer = setTimeout(() => {
          isLongPress = true
          const { lng, lat } = e.lnglat
          onLongPress({ lat, lng })
          // 震动反馈
          if ('vibrate' in navigator) {
            navigator.vibrate(50)
          }
        }, 800)
      })

      // 鼠标移动事件（桌面端）
      map.on('mousemove', () => {
        if (pressTimer) {
          clearTimeout(pressTimer)
          pressTimer = null
        }
      })

      // 鼠标抬起事件（桌面端）
      map.on('mouseup', () => {
        if (pressTimer) {
          clearTimeout(pressTimer)
          pressTimer = null
        }
      })

      // 移动端触摸事件
      map.on('touchstart', (e: any) => {
        isLongPress = false
        pressTimer = setTimeout(() => {
          isLongPress = true
          const { lng, lat } = e.lnglat
          onLongPress({ lat, lng })
          // 震动反馈
          if ('vibrate' in navigator) {
            navigator.vibrate(50)
          }
        }, 800)
      })

      map.on('touchmove', () => {
        if (pressTimer) {
          clearTimeout(pressTimer)
          pressTimer = null
        }
      })

      map.on('touchend', () => {
        if (pressTimer) {
          clearTimeout(pressTimer)
          pressTimer = null
        }
      })

      // 禁用右键菜单
      map.on('rightclick', (e: any) => {
        e.preventDefault()
      })

      mapInstanceRef.current = map
    } catch (err) {
      setError('地图初始化失败')
      console.error('Map initialization error:', err)
    }
  }, [isLoaded, onLongPress])

  // 更新标记点
  useEffect(() => {
    if (!mapInstanceRef.current || !isLoaded) return

    // 清除现有标记
    markersRef.current.forEach(marker => {
      mapInstanceRef.current.remove(marker)
    })
    markersRef.current = []

    // 添加新标记
    markers.forEach(markerData => {
      try {
        const iconData = getIconById(markerData.iconId || 'location')
        if (!iconData) return

        // 创建自定义标记内容
        const markerContent = document.createElement('div')
        markerContent.className = 'relative cursor-pointer group'
        markerContent.innerHTML = `
          <div class="w-10 h-10 ${iconData.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </div>
          ${markerData.isPublic ? `
            <div class="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
              <div class="w-2 h-2 bg-white rounded-full"></div>
            </div>
          ` : ''}
        `

        const marker = new window.AMap.Marker({
          position: [markerData.lng, markerData.lat],
          content: markerContent,
          anchor: 'bottom-center',
          offset: new window.AMap.Pixel(0, 0)
        })

        // 点击事件
        marker.on('click', () => {
          onMarkerClick({ ...markerData, icon: iconData })
        })

        mapInstanceRef.current.add(marker)
        markersRef.current.push(marker)
      } catch (err) {
        console.error('Error creating marker:', err)
      }
    })
  }, [markers, isLoaded, onMarkerClick])

  // 获取当前位置
  const getCurrentLocation = () => {
    if (!mapInstanceRef.current) return

    setIsLocationLoading(true)
    
    mapInstanceRef.current.plugin('AMap.Geolocation', () => {
      const geolocation = new window.AMap.Geolocation({
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
        convert: true,
        showButton: false,
        buttonPosition: 'LB',
        buttonOffset: new window.AMap.Pixel(10, 20),
        showMarker: true,
        showCircle: true,
        panToLocation: true,
        zoomToAccuracy: true
      })

      geolocation.getCurrentPosition((status: string, result: any) => {
        setIsLocationLoading(false)
        if (status === 'complete') {
          console.log('定位成功:', result)
          // 震动反馈
          if ('vibrate' in navigator) {
            navigator.vibrate(30)
          }
        } else {
          console.error('定位失败:', result)
        }
      })
    })
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`} style={style}>
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">地图加载失败</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  if (!isLoaded) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`} style={style}>
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-gray-600">正在加载地图...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} style={style}>
      <div ref={mapRef} className="w-full h-full" />
      
      {/* 定位按钮 - 放在地图容器内的右上角，避免遮挡头部按钮 */}
      <Button
        className="absolute top-4 right-4 w-12 h-12 rounded-2xl shadow-lg shadow-blue-500/25 bg-blue-500 hover:bg-blue-600 z-[5]"
        onClick={getCurrentLocation}
        disabled={isLocationLoading}
        size="icon"
      >
        <Navigation className={`w-5 h-5 ${isLocationLoading ? "animate-spin" : ""}`} />
      </Button>
    </div>
  )
}