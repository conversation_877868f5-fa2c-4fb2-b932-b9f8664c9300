"use client"

import { <PERSON>alog, DialogContent } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Calendar, Share2, Edit, Trash2, Eye, EyeOff, Coffee } from "lucide-react"
import { getIconComponentById } from "@/components/icons/icon-library"
import { useMemo } from "react"
import type { Marker } from "@/lib/types"

interface MarkerDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  marker: Marker | null
  onShare: () => void
  onEdit?: (marker: Marker) => void
  onDelete?: (markerId: string) => void
}

export function MarkerDetailDialog({ open, onOpenChange, marker, onShare, onEdit, onDelete }: MarkerDetailDialogProps) {
  // 安全获取图标组件
  const IconComponent = useMemo(() => {
    if (!marker) return Coffee

    if (typeof marker.icon === "string") {
      return getIconComponentById(marker.icon) || Coffee
    }
    if (typeof marker.iconId === "string") {
      return getIconComponentById(marker.iconId) || Coffee
    }
    if (marker.icon && typeof marker.icon === "function") {
      return marker.icon
    }
    return Coffee
  }, [marker])

  // 如果没有marker数据，不渲染对话框
  if (!marker) return null

  const handleEdit = () => {
    if (onEdit) {
      onEdit(marker)
    }
    onOpenChange(false)
  }

  const handleDelete = () => {
    if (onDelete && confirm("确定要删除这个收藏吗？")) {
      onDelete(marker.id)
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <div className="space-y-6">
          {/* 头部信息 */}
          <div className="flex items-start gap-4">
            <div
              className={`w-16 h-16 ${marker.color || "bg-gray-500"} rounded-2xl flex items-center justify-center shadow-lg`}
            >
              <IconComponent className="w-8 h-8 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{marker.title || marker.name}</h2>
              <div className="flex items-center gap-2 mb-3">
                <Badge variant="secondary" className="text-sm">
                  {marker.category}
                </Badge>
                {marker.isPublic ? (
                  <Badge variant="outline" className="text-green-600 border-green-200">
                    <Eye className="w-3 h-3 mr-1" />
                    公开
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-gray-600 border-gray-200">
                    <EyeOff className="w-3 h-3 mr-1" />
                    私密
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* 图片展示 */}
          {marker.image && (
            <div className="w-full h-48 bg-gray-100 rounded-xl overflow-hidden">
              <img
                src={marker.image || "/placeholder.svg"}
                alt={marker.title || marker.name}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
          )}

          {/* 描述信息 */}
          {marker.description && (
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900">描述</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{marker.description}</p>
            </div>
          )}

          {/* 位置和时间信息 */}
          <div className="space-y-3">
            <div className="flex items-start gap-3 text-sm text-gray-600">
              <MapPin className="w-4 h-4 mt-0.5 text-gray-400" />
              <div className="flex-1">
                <p className="font-medium text-gray-900">位置</p>
                <p>{marker.location || marker.address || `${marker.lat?.toFixed(4)}, ${marker.lng?.toFixed(4)}`}</p>
              </div>
            </div>
            <div className="flex items-start gap-3 text-sm text-gray-600">
              <Calendar className="w-4 h-4 mt-0.5 text-gray-400" />
              <div className="flex-1">
                <p className="font-medium text-gray-900">添加时间</p>
                <p>{marker.date || "今天"}</p>
              </div>
            </div>
          </div>

          {/* 标签 */}
          {marker.tags && marker.tags.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">标签</h3>
              <div className="flex gap-2 flex-wrap">
                {marker.tags.map((tag: string, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 地图预览 */}
          <div className="h-32 bg-gradient-to-br from-blue-50 to-green-50 rounded-xl relative overflow-hidden border border-gray-100">
            <div className="absolute inset-0 flex items-center justify-center">
              <div
                className={`w-10 h-10 ${marker.color || "bg-gray-500"} rounded-full flex items-center justify-center shadow-lg`}
              >
                <IconComponent className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="absolute bottom-3 left-3 bg-black/70 text-white text-xs px-3 py-1.5 rounded-full">
              点击查看完整地图
            </div>
            <div className="absolute top-3 right-3 text-xs text-gray-500 bg-white/80 px-2 py-1 rounded-full">
              {marker.lat?.toFixed(4)}, {marker.lng?.toFixed(4)}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3 pt-2">
            <Button variant="outline" className="flex-1 h-12 bg-white hover:bg-gray-50" onClick={onShare}>
              <Share2 className="w-4 h-4 mr-2" />
              分享
            </Button>
            <Button variant="outline" className="flex-1 h-12 bg-white hover:bg-gray-50" onClick={handleEdit}>
              <Edit className="w-4 h-4 mr-2" />
              编辑
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-12 w-12 text-red-600 hover:text-red-700 hover:bg-red-50 bg-white"
              onClick={handleDelete}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
