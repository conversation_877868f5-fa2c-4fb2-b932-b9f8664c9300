"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Database, Cloud, Download, Upload, Shield, Map, User, Info, ChevronRight } from "lucide-react"

interface SettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>设置</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 数据管理 */}
          <div>
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Database className="w-4 h-4" />
              数据管理
            </h3>
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <div className="flex items-center gap-2">
                  <Download className="w-4 h-4" />
                  <span>导出数据</span>
                </div>
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <div className="flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  <span>导入数据</span>
                </div>
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <div className="flex items-center gap-2">
                  <Cloud className="w-4 h-4" />
                  <span>云端同步</span>
                </div>
                <div className="text-sm text-gray-500">未登录</div>
              </Button>
            </div>
          </div>

          <Separator />

          {/* 隐私设置 */}
          <div>
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Shield className="w-4 h-4" />
              隐私设置
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">默认公开状态</Label>
                  <div className="text-xs text-gray-500">新添加的收藏点默认为公开</div>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">位置记录</Label>
                  <div className="text-xs text-gray-500">允许记录当前位置信息</div>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>

          <Separator />

          {/* 地图设置 */}
          <div>
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Map className="w-4 h-4" />
              地图设置
            </h3>
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <span>地图服务</span>
                <div className="text-sm text-gray-500">高德地图</div>
              </Button>
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">离线地图</Label>
                  <div className="text-xs text-gray-500">缓存地图数据以提升体验</div>
                </div>
                <Switch />
              </div>
            </div>
          </div>

          <Separator />

          {/* 账户设置 */}
          <div>
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <User className="w-4 h-4" />
              账户设置
            </h3>
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <span>登录账号</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <Separator />

          {/* 关于 */}
          <div>
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Info className="w-4 h-4" />
              关于
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>版本号</span>
                <span className="text-gray-500">1.0.0</span>
              </div>
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <span>使用协议</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button variant="outline" className="w-full justify-between bg-transparent">
                <span>意见反馈</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
