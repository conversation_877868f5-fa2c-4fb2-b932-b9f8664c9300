"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Link2, Copy, QrCode, MessageCircle, Share2, Lock, Eye, Coffee } from "lucide-react"
import { MobileShareSheet } from "@/components/mobile-share-sheet"

interface ShareDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  marker: any
}

export function ShareDialog({ open, onOpenChange, marker }: ShareDialogProps) {
  const [isPublic, setIsPublic] = useState(marker?.isPublic || false)
  const [requirePassword, setRequirePassword] = useState(false)
  const [password, setPassword] = useState("")
  const [shareUrl] = useState("https://favsany.app/share/abc123")
  const [showMobileShare, setShowMobileShare] = useState(false)

  if (!marker) return null

  const IconComponent = marker.icon || Coffee

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl)
    // 这里可以添加复制成功的提示
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>分享收藏点</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 预览卡片 */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center gap-3 mb-2">
              <div className={`w-10 h-10 ${marker.color || "bg-gray-500"} rounded-lg flex items-center justify-center`}>
                <IconComponent className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-medium">{marker.title}</h3>
                <div className="text-sm text-gray-500">{marker.category}</div>
              </div>
            </div>
            {marker.description && <p className="text-sm text-gray-600 line-clamp-2">{marker.description}</p>}
          </div>

          {/* 分享设置 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">公开访问</Label>
                <div className="text-xs text-gray-500">允许任何人通过链接访问</div>
              </div>
              <Switch checked={isPublic} onCheckedChange={setIsPublic} />
            </div>

            {isPublic && (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">密码保护</Label>
                    <div className="text-xs text-gray-500">需要密码才能查看</div>
                  </div>
                  <Switch checked={requirePassword} onCheckedChange={setRequirePassword} />
                </div>

                {requirePassword && (
                  <div className="space-y-2">
                    <Label htmlFor="password">访问密码</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="设置访问密码"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                )}
              </>
            )}
          </div>

          {/* 分享链接 */}
          {isPublic && (
            <div className="space-y-2">
              <Label>分享链接</Label>
              <div className="flex gap-2">
                <Input value={shareUrl} readOnly className="flex-1" />
                <Button variant="outline" size="icon" onClick={handleCopyLink}>
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                {requirePassword ? (
                  <>
                    <Lock className="w-3 h-3" />
                    <span>需要密码访问</span>
                  </>
                ) : (
                  <>
                    <Eye className="w-3 h-3" />
                    <span>公开可访问</span>
                  </>
                )}
              </div>
            </div>
          )}

          {/* 分享方式 */}
          {isPublic && (
            <div className="space-y-2">
              <Label>分享到</Label>
              <div className="grid grid-cols-4 gap-2">
                <Button variant="outline" className="flex flex-col items-center gap-1 h-16 bg-transparent">
                  <MessageCircle className="w-5 h-5" />
                  <span className="text-xs">微信</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center gap-1 h-16 bg-transparent">
                  <Share2 className="w-5 h-5" />
                  <span className="text-xs">朋友圈</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center gap-1 h-16 bg-transparent">
                  <QrCode className="w-5 h-5" />
                  <span className="text-xs">二维码</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center gap-1 h-16 bg-transparent">
                  <Link2 className="w-5 h-5" />
                  <span className="text-xs">复制链接</span>
                </Button>
              </div>
            </div>
          )}

          {!isPublic && (
            <div className="text-center py-8 text-gray-500">
              <Lock className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm">开启公开访问后可生成分享链接</div>
            </div>
          )}

          {/* Mobile Share Button */}
          <Button className="w-full mb-4" onClick={() => setShowMobileShare(true)}>
            <Share2 className="w-4 h-4 mr-2" />
            移动端分享
          </Button>

          {/* Mobile Share Sheet */}
          <MobileShareSheet
            open={showMobileShare}
            onOpenChange={setShowMobileShare}
            data={{
              title: marker.title,
              description: marker.description,
              url: shareUrl,
              image: marker.image,
            }}
          />

          {/* 操作按钮 */}
          <div className="flex gap-2 pt-4">
            <Button variant="outline" className="flex-1 bg-transparent" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button className="flex-1" disabled={!isPublic}>
              确认分享
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
