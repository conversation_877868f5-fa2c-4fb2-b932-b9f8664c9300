"use client"

import { useState } from "react"
import { useMobileFeatures } from "@/hooks/use-mobile-features"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Share2, Copy, QrCode, MessageCircle, Mail, Download, Smartphone, X } from "lucide-react"

interface MobileShareSheetProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: {
    title: string
    description?: string
    url: string
    image?: string
  }
}

export function MobileShareSheet({ open, onOpenChange, data }: MobileShareSheetProps) {
  const { shareToSystem, vibrate, addToHomeScreen } = useMobileFeatures()
  const [showQR, setShowQR] = useState(false)

  const handleSystemShare = async () => {
    vibrate(30)
    const success = await shareToSystem({
      title: data.title,
      text: data.description || "",
      url: data.url,
    })

    if (success) {
      onOpenChange(false)
    }
  }

  const handleCopyLink = async () => {
    vibrate(30)
    try {
      await navigator.clipboard.writeText(data.url)
      // 可以添加复制成功的提示
    } catch (error) {
      console.error("Failed to copy:", error)
    }
  }

  const handleAddToHome = async () => {
    vibrate(50)
    try {
      const result = await addToHomeScreen()
      console.log("Add to home screen result:", result)
    } catch (error) {
      console.error("Failed to add to home screen:", error)
    }
  }

  const shareOptions = [
    {
      icon: Share2,
      label: "系统分享",
      action: handleSystemShare,
      available: !!navigator.share,
    },
    {
      icon: Copy,
      label: "复制链接",
      action: handleCopyLink,
      available: true,
    },
    {
      icon: QrCode,
      label: "二维码",
      action: () => {
        vibrate(30)
        setShowQR(true)
      },
      available: true,
    },
    {
      icon: MessageCircle,
      label: "微信",
      action: () => {
        vibrate(30)
        // 这里可以集成微信 SDK
      },
      available: true,
    },
    {
      icon: Mail,
      label: "邮件",
      action: () => {
        vibrate(30)
        window.location.href = `mailto:?subject=${encodeURIComponent(data.title)}&body=${encodeURIComponent(data.url)}`
      },
      available: true,
    },
    {
      icon: Smartphone,
      label: "添加到主屏幕",
      action: handleAddToHome,
      available: true,
    },
  ]

  if (showQR) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-sm">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">分享二维码</h3>
              <Button variant="ghost" size="icon" onClick={() => setShowQR(false)}>
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* 这里应该生成真实的二维码 */}
            <div className="w-48 h-48 bg-gray-100 mx-auto rounded-lg flex items-center justify-center">
              <QrCode className="w-16 h-16 text-gray-400" />
            </div>

            <p className="text-sm text-gray-600">扫描二维码访问收藏点</p>

            <Button variant="outline" className="w-full bg-transparent" onClick={handleCopyLink}>
              <Copy className="w-4 h-4 mr-2" />
              复制链接
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-sm">
        <div className="space-y-4">
          {/* Header */}
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">分享收藏点</h3>
            <div className="text-sm text-gray-600 line-clamp-2">{data.title}</div>
          </div>

          {/* Share Options Grid */}
          <div className="grid grid-cols-3 gap-4">
            {shareOptions
              .filter((option) => option.available)
              .map((option, index) => {
                const IconComponent = option.icon
                return (
                  <Button
                    key={index}
                    variant="ghost"
                    className="flex flex-col items-center gap-2 h-20 bg-transparent hover:bg-gray-50"
                    onClick={option.action}
                  >
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <span className="text-xs">{option.label}</span>
                  </Button>
                )
              })}
          </div>

          {/* Quick Actions */}
          <div className="space-y-2 pt-4 border-t">
            <Button
              variant="outline"
              className="w-full bg-transparent"
              onClick={() => {
                vibrate(30)
                // 可以添加下载功能
              }}
            >
              <Download className="w-4 h-4 mr-2" />
              保存到相册
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
