"use client"

import type React from "react"

import { useState, useRef } from "react"
import { useTouchGestures } from "@/hooks/use-touch-gestures"
import { useMobileFeatures } from "@/hooks/use-mobile-features"
import { But<PERSON> } from "@/components/ui/button"
import { Edit, Trash2, Share2, Eye, EyeOff } from "lucide-react"

interface SwipeableListItemProps {
  children: React.ReactNode
  onEdit?: () => void
  onDelete?: () => void
  onShare?: () => void
  onToggleVisibility?: () => void
  isPublic?: boolean
}

export function SwipeableListItem({
  children,
  onEdit,
  onDelete,
  onShare,
  onToggleVisibility,
  isPublic = false,
}: SwipeableListItemProps) {
  const [swipeOffset, setSwipeOffset] = useState(0)
  const [isRevealed, setIsRevealed] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const { vibrate } = useMobileFeatures()

  const touchGestures = useTouchGestures({
    onSwipeLeft: () => {
      if (!isRevealed) {
        setSwipeOffset(-120)
        setIsRevealed(true)
        vibrate(30)
      }
    },
    onSwipeRight: () => {
      if (isRevealed) {
        setSwipeOffset(0)
        setIsRevealed(false)
        vibrate(30)
      }
    },
    onTap: () => {
      if (isRevealed) {
        setSwipeOffset(0)
        setIsRevealed(false)
      }
    },
  })

  const handleAction = (action: () => void) => {
    vibrate(50)
    setSwipeOffset(0)
    setIsRevealed(false)
    action()
  }

  return (
    <div className="relative overflow-hidden bg-white rounded-lg" ref={containerRef}>
      {/* 背景操作按钮 */}
      <div className="absolute right-0 top-0 h-full flex items-center bg-gray-100">
        <div className="flex h-full">
          {onToggleVisibility && (
            <Button
              variant="ghost"
              size="icon"
              className="h-full w-12 rounded-none bg-blue-500 hover:bg-blue-600 text-white"
              onClick={() => handleAction(onToggleVisibility)}
            >
              {isPublic ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          )}
          {onShare && (
            <Button
              variant="ghost"
              size="icon"
              className="h-full w-12 rounded-none bg-green-500 hover:bg-green-600 text-white"
              onClick={() => handleAction(onShare)}
            >
              <Share2 className="w-4 h-4" />
            </Button>
          )}
          {onEdit && (
            <Button
              variant="ghost"
              size="icon"
              className="h-full w-12 rounded-none bg-orange-500 hover:bg-orange-600 text-white"
              onClick={() => handleAction(onEdit)}
            >
              <Edit className="w-4 h-4" />
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-full w-12 rounded-none bg-red-500 hover:bg-red-600 text-white"
              onClick={() => handleAction(onDelete)}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 主内容 */}
      <div
        className="relative bg-white transition-transform duration-200 ease-out"
        style={{ transform: `translateX(${swipeOffset}px)` }}
        {...touchGestures}
      >
        {children}
      </div>

      {/* 滑动提示 */}
      {!isRevealed && swipeOffset === 0 && (
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-400 pointer-events-none">
          ← 滑动
        </div>
      )}
    </div>
  )
}
