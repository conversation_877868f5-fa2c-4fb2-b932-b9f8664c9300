"use client"

import { useState, useCallback } from "react"

export function useMobileFeatures() {
  const [isLocationLoading, setIsLocationLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)

  // 获取当前位置
  const getCurrentLocation = useCallback((): Promise<{ lat: number; lng: number }> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported"))
        return
      }

      setIsLocationLoading(true)
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setIsLocationLoading(false)
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          })
        },
        (error) => {
          setIsLocationLoading(false)
          reject(error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000,
        },
      )
    })
  }, [])

  // 拍照功能
  const takePhoto = useCallback((): Promise<string> => {
    return new Promise((resolve, reject) => {
      const input = document.createElement("input")
      input.type = "file"
      input.accept = "image/*"
      input.capture = "environment" // 使用后置摄像头

      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (file) {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result as string)
          reader.onerror = () => reject(new Error("Failed to read file"))
          reader.readAsDataURL(file)
        } else {
          reject(new Error("No file selected"))
        }
      }

      input.click()
    })
  }, [])

  // 语音录制
  const startVoiceRecording = useCallback((): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        reject(new Error("Media recording not supported"))
        return
      }

      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const mediaRecorder = new MediaRecorder(stream)
          const chunks: Blob[] = []

          mediaRecorder.ondataavailable = (e) => {
            chunks.push(e.data)
          }

          mediaRecorder.onstop = () => {
            const blob = new Blob(chunks, { type: "audio/wav" })
            stream.getTracks().forEach((track) => track.stop())
            resolve(blob)
            setIsRecording(false)
          }

          mediaRecorder.start()
          setIsRecording(true)

          // 自动停止录制（最长60秒）
          setTimeout(() => {
            if (mediaRecorder.state === "recording") {
              mediaRecorder.stop()
            }
          }, 60000)

          // 返回停止录制的函数
          ;(resolve as any).stop = () => mediaRecorder.stop()
        })
        .catch(reject)
    })
  }, [])

  // 震动反馈
  const vibrate = useCallback((pattern: number | number[] = 50) => {
    if ("vibrate" in navigator) {
      navigator.vibrate(pattern)
    }
  }, [])

  // 分享到系统
  const shareToSystem = useCallback(async (data: { title: string; text: string; url?: string }) => {
    if (navigator.share) {
      try {
        await navigator.share(data)
        return true
      } catch (error) {
        console.error("Error sharing:", error)
        return false
      }
    }
    return false
  }, [])

  // 添加到主屏幕
  const addToHomeScreen = useCallback(() => {
    // 这个功能需要 PWA 配置
    const event = (window as any).deferredPrompt
    if (event) {
      event.prompt()
      return event.userChoice
    }
    return Promise.resolve({ outcome: "dismissed" })
  }, [])

  // 全屏模式
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.()
    } else {
      document.exitFullscreen?.()
    }
  }, [])

  return {
    getCurrentLocation,
    takePhoto,
    startVoiceRecording,
    vibrate,
    shareToSystem,
    addToHomeScreen,
    toggleFullscreen,
    isLocationLoading,
    isRecording,
  }
}
