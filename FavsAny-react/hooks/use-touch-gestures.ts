"use client"

import { useRef, useCallback } from "react"

interface TouchGestureOptions {
  onTap?: (e: TouchEvent) => void
  onLongPress?: (e: TouchEvent) => void
  onSwipeLeft?: (e: TouchEvent) => void
  onSwipeRight?: (e: TouchEvent) => void
  onSwipeUp?: (e: TouchEvent) => void
  onSwipeDown?: (e: TouchEvent) => void
  onPinch?: (scale: number, e: TouchEvent) => void
  onDoubleTap?: (e: TouchEvent) => void
  longPressDelay?: number
  swipeThreshold?: number
}

export function useTouchGestures(options: TouchGestureOptions = {}) {
  const {
    onTap,
    onLongPress,
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onPinch,
    onDoubleTap,
    longPressDelay = 500,
    swipeThreshold = 50,
  } = options

  const touchStart = useRef<{ x: number; y: number; time: number } | null>(null)
  const longPressTimer = useRef<NodeJS.Timeout | null>(null)
  const lastTap = useRef<number>(0)
  const initialDistance = useRef<number>(0)

  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      const touch = e.touches[0]
      touchStart.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now(),
      }

      // 处理双指缩放
      if (e.touches.length === 2) {
        const touch1 = e.touches[0]
        const touch2 = e.touches[1]
        initialDistance.current = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2),
        )
        return // 双指操作时不触发长按
      }

      // 长按检测
      if (onLongPress) {
        longPressTimer.current = setTimeout(() => {
          // 添加震动反馈
          if ("vibrate" in navigator) {
            navigator.vibrate(50)
          }
          onLongPress(e)
          longPressTimer.current = null
        }, longPressDelay)
      }
    },
    [onLongPress, longPressDelay],
  )

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!touchStart.current) return

      const touch = e.touches[0]
      const deltaX = touch.clientX - touchStart.current.x
      const deltaY = touch.clientY - touchStart.current.y
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

      // 如果移动距离超过阈值，取消长按
      if (distance > 10 && longPressTimer.current) {
        clearTimeout(longPressTimer.current)
        longPressTimer.current = null
      }

      // 处理双指缩放
      if (e.touches.length === 2 && onPinch) {
        const touch1 = e.touches[0]
        const touch2 = e.touches[1]
        const currentDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2),
        )

        if (initialDistance.current > 0) {
          const scale = currentDistance / initialDistance.current
          onPinch(scale, e)
        }
      }
    },
    [onPinch],
  )

  const handleTouchEnd = useCallback(
    (e: TouchEvent) => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
        longPressTimer.current = null
      }

      if (!touchStart.current) return

      const touch = e.changedTouches[0]
      const deltaX = touch.clientX - touchStart.current.x
      const deltaY = touch.clientY - touchStart.current.y
      const deltaTime = Date.now() - touchStart.current.time
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

      // 双击检测
      if (onDoubleTap && deltaTime < 300 && distance < 10) {
        const now = Date.now()
        if (now - lastTap.current < 300) {
          onDoubleTap(e)
          lastTap.current = 0
          return
        }
        lastTap.current = now
      }

      // 滑动检测
      if (distance > swipeThreshold && deltaTime < 300) {
        const absX = Math.abs(deltaX)
        const absY = Math.abs(deltaY)

        if (absX > absY) {
          // 水平滑动
          if (deltaX > 0 && onSwipeRight) {
            onSwipeRight(e)
          } else if (deltaX < 0 && onSwipeLeft) {
            onSwipeLeft(e)
          }
        } else {
          // 垂直滑动
          if (deltaY > 0 && onSwipeDown) {
            onSwipeDown(e)
          } else if (deltaY < 0 && onSwipeUp) {
            onSwipeUp(e)
          }
        }
      } else if (distance < 10 && deltaTime < 300 && onTap) {
        // 点击
        setTimeout(() => {
          if (Date.now() - lastTap.current > 300) {
            onTap(e)
          }
        }, 300)
      }

      touchStart.current = null
      initialDistance.current = 0
    },
    [onTap, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onDoubleTap, swipeThreshold],
  )

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  }
}
