"use client"

import { useState, Suspense } from "react"
import { MapView } from "@/components/map-view"
import { CollectionsList } from "@/components/collections-list"
import { AddMarkerDialog } from "@/components/add-marker-dialog"
import { MarkerDetailDialog } from "@/components/marker-detail-dialog"
import { SettingsDialog } from "@/components/settings-dialog"
import { ShareDialog } from "@/components/share-dialog"

function HomePage() {
  const [currentView, setCurrentView] = useState<"map" | "favorites">("map")
  const [selectedMarker, setSelectedMarker] = useState<any>(null)
  const [isAddingMarker, setIsAddingMarker] = useState(false)
  const [newMarkerPosition, setNewMarkerPosition] = useState<{ lat: number; lng: number } | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [showShare, setShowShare] = useState(false)

  const handleMapLongPress = (position: { lat: number; lng: number }) => {
    setNewMarkerPosition(position)
    setIsAddingMarker(true)
  }

  const handleMarkerClick = (marker: any) => {
    setSelectedMarker(marker)
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {currentView === "map" ? (
        <MapView
          onLongPress={handleMapLongPress}
          onMarkerClick={handleMarkerClick}
          onFavoritesClick={() => setCurrentView("favorites")}
          onSettingsClick={() => setShowSettings(true)}
        />
      ) : (
        <CollectionsList onBackClick={() => setCurrentView("map")} onMarkerClick={handleMarkerClick} />
      )}

      <AddMarkerDialog open={isAddingMarker} onOpenChange={setIsAddingMarker} position={newMarkerPosition} />

      <MarkerDetailDialog
        open={!!selectedMarker}
        onOpenChange={(open) => !open && setSelectedMarker(null)}
        marker={selectedMarker}
        onShare={() => setShowShare(true)}
      />

      <SettingsDialog open={showSettings} onOpenChange={setShowSettings} />

      <ShareDialog open={showShare} onOpenChange={setShowShare} marker={selectedMarker} />
    </div>
  )
}

export default function Page() {
  return (
    <Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
      <HomePage />
    </Suspense>
  )
}