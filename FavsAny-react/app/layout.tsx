import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'FavsAny - 个人兴趣收藏工具',
  description: '基于地理位置的个人兴趣收藏工具',
  generator: 'v0.dev',
  manifest: '/manifest.json',
  themeColor: '#3b82f6',
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#3b82f6" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" />
        {/* 预加载高德地图 */}
        <link rel="preconnect" href="https://webapi.amap.com" />
        <link rel="preconnect" href="https://restapi.amap.com" />
      </head>
      <body className="overflow-hidden">{children}</body>
    </html>
  )
}