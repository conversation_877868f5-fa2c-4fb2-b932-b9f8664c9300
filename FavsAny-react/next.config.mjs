/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  // Fix for client-side routing issues
  trailingSlash: false,
  skipTrailingSlashRedirect: true,
  // 允许加载高德地图外部资源
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://webapi.amap.com https://restapi.amap.com; connect-src 'self' https://webapi.amap.com https://restapi.amap.com; img-src 'self' data: https: blob:; style-src 'self' 'unsafe-inline' https:;",
          },
        ],
      },
    ]
  },
}

export default nextConfig