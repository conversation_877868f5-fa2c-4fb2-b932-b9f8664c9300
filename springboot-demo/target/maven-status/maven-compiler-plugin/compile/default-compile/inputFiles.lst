/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/entity/BaseEntity.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/DemoApplication.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/mapper/BaseMapperInterface.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/entity/User.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/service/UserServiceImpl.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/util/JwtUtil.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/util/PasswordUtil.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/mapper/UserMapper.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/service/UserService.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/dto/AuthResponse.java
/Users/<USER>/ai-workspace/springboot-demo/src/main/java/com/example/demo/controller/LoginController.java
