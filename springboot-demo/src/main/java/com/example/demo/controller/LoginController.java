package com.example.demo.controller;

import com.example.demo.service.UserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;

@RestController
@RequestMapping("/api/auth")
public class LoginController {
    private final UserService userService;

    public LoginController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestParam String username, 
                                  @RequestParam String password) {
        return userService.authenticateUser(username, password);
    }

    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestParam String username,
                                    @RequestParam String password,
                                    @RequestParam String email) {
        return userService.registerUser(username, password, email);
    }
}
