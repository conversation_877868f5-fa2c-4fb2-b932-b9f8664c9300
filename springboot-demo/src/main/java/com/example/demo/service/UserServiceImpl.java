package com.example.demo.service;

import com.example.demo.entity.User;
import com.example.demo.mapper.UserMapper;
import com.example.demo.util.PasswordUtil;
import com.example.demo.util.JwtUtil;
import com.example.demo.dto.AuthResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.http.HttpStatus;

@Service
public class UserServiceImpl implements UserService {
    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;

    public UserServiceImpl(UserMapper userMapper, JwtUtil jwtUtil) {
        this.userMapper = userMapper;
        this.jwtUtil = jwtUtil;
    }

    @Override
    public ResponseEntity<?> authenticateUser(String username, String password) {
        if (username == null || username.isBlank() || password == null || password.isBlank()) {
            return ResponseEntity.badRequest().body("Username and password are required");
        }

        User user = userMapper.findByUsername(username);
        if (user == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }

        // Verify password hash
        if (!PasswordUtil.verifyPassword(password, user.getPassword())) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
        }

        // Generate JWT token
        String token = jwtUtil.generateToken(user);
        return ResponseEntity.ok(new AuthResponse(token, user));
    }

    @Override
    public ResponseEntity<?> registerUser(String username, String password, String email) {
        // Validate input
        if (username == null || username.isBlank() || 
            password == null || password.isBlank() || 
            email == null || email.isBlank()) {
            return ResponseEntity.badRequest().body("All fields are required");
        }

        // Validate email format
        if (!email.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
            return ResponseEntity.badRequest().body("Invalid email format");
        }

        // Check password strength
        if (!PasswordUtil.isStrongPassword(password)) {
            return ResponseEntity.badRequest().body(
                "Password must be at least 8 characters long and contain uppercase, lowercase, numbers and special characters"
            );
        }

        // Check if username exists
        if (userMapper.findByUsername(username) != null) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body("Username already exists");
        }

        // Check if email exists
        if (userMapper.findByEmail(email) != null) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body("Email already registered");
        }

        // Create new user
        User newUser = new User();
        newUser.setUsername(username);
        newUser.setPassword(PasswordUtil.hashPassword(password));
        newUser.setEmail(email);
        
        try {
            userMapper.insert(newUser);
            return ResponseEntity.status(HttpStatus.CREATED).body("User registered successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Registration failed: " + e.getMessage());
        }
    }
}
