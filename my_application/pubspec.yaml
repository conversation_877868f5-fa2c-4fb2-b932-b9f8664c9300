name: favsany
description: "FavsAny - 万物收藏，一个全能的个人兴趣收藏管理工具"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # 网络请求
  http: ^1.1.0
  dio: ^5.4.0

  # UI组件
  cupertino_icons: ^1.0.8

  # 地图相关
  google_maps_flutter: ^2.5.0
  webview_flutter: ^4.4.2
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # 数据库
  sqflite: ^2.3.0
  path_provider: ^2.1.2

  # 权限管理
  permission_handler: ^10.4.3

  # 图片处理
  image_picker: ^1.0.7
  cached_network_image: ^3.3.1
  photo_view: ^0.14.0

  # 文件管理
  path: ^1.8.3

  # 状态管理
  provider: ^6.1.1

  # 分享功能
  share_plus: ^7.2.2
  qr_flutter: ^4.1.0

  # 本地存储
  shared_preferences: ^2.2.2

  # 日期时间
  intl: ^0.19.0

  # 工具类
  uuid: ^4.3.3

  # 动画
  lottie: ^3.0.0
  heroicons: ^0.11.0

  # 移动端特性
  vibration: ^1.8.4
  device_info_plus: ^10.1.0
  url_launcher: ^6.2.5



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Noto Sans SC
      fonts:
        - asset: fonts/NotoSansSC-Regular.ttf
        - asset: fonts/NotoSansSC-Medium.ttf
          weight: 500
        - asset: fonts/NotoSansSC-Bold.ttf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
