# Google Maps API Key 配置指南

## 🗝️ 获取 Google Maps API Key

### 1. 访问 Google Cloud Console
- 打开 [Google Cloud Console](https://console.cloud.google.com/)
- 登录您的 Google 账户

### 2. 创建或选择项目
- 创建新项目或选择现有项目
- 记住项目名称

### 3. 启用必要的 API
在 "API 和服务" > "库" 中启用以下 API：
- **Maps JavaScript API** (用于 Web 版本)
- **Maps SDK for iOS** (用于 iOS 版本)
- **Maps SDK for Android** (用于 Android 版本)

### 4. 创建 API Key
1. 转到 "API 和服务" > "凭据"
2. 点击 "创建凭据" > "API 密钥"
3. 复制生成的 API Key

### 5. 配置 API Key 限制（推荐）
为了安全，建议限制 API Key 的使用：
- **应用程序限制**: 选择适当的限制类型
- **API 限制**: 限制为上述启用的 Maps API

## 🔧 在应用中配置 API Key

### iOS 配置
编辑文件：`ios/Runner/AppDelegate.swift`
```swift
GMSServices.provideAPIKey("您的_实际_API_KEY")
```

### Android 配置
编辑文件：`android/app/src/main/AndroidManifest.xml`
```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="您的_实际_API_KEY" />
```

### Web 配置
编辑文件：`web/index.html`
```html
<script src="https://maps.googleapis.com/maps/api/js?key=您的_实际_API_KEY"></script>
```

## 🚀 重新启动应用

配置完成后，重新启动应用：
```bash
flutter run -d ios
```

## 💡 注意事项

1. **保护您的 API Key**: 不要将 API Key 提交到公共代码仓库
2. **设置使用限制**: 在 Google Cloud Console 中设置每日使用限制
3. **监控使用情况**: 定期检查 API 使用情况和费用
4. **测试环境**: 可以为开发和生产环境使用不同的 API Key

## 🔍 故障排除

如果地图仍然无法显示：
1. 检查 API Key 是否正确配置
2. 确认已启用相应的 Maps API
3. 检查 API Key 的限制设置
4. 查看控制台错误信息
5. 确认网络连接正常

## 📱 当前状态

- ✅ iOS 配置文件已准备就绪
- ✅ Android 配置文件已准备就绪  
- ✅ Web 配置文件已准备就绪
- ⏳ 需要您提供真实的 Google Maps API Key

配置完成后，您的 FavsAny 应用将能够正常显示 Google Maps！
