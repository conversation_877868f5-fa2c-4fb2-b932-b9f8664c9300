<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="FavsAny - 万物收藏，一个全能的个人兴趣收藏管理工具">

  <!-- 添加中文字体支持 -->
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
  </style>

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="my_application">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>FavsAny</title>
  <link rel="manifest" href="manifest.json">

  <!-- 中文字体样式 -->
  <style>
    body {
      font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }

    /* 确保Flutter应用也使用正确的字体 */
    flt-glass-pane * {
      font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
    }
  </style>

  <!-- 高德地图JavaScript API v2.0 - 按照官方文档配置 -->
  <script>
    // 设置安全密钥
    window._AMapSecurityConfig = {
      securityJsCode: "e02aa654323adbcfdf59a3ae268f586d",
    };
  </script>
  <script src="https://webapi.amap.com/maps?v=2.0&key=e02aa654323adbcfdf59a3ae268f586d"></script>

  <!-- 添加调试信息 -->
  <script>
    window.addEventListener('load', function() {
      console.log('页面加载完成');
      console.log('AMap对象:', typeof AMap !== 'undefined' ? 'available' : 'not available');
      if (typeof AMap !== 'undefined') {
        console.log('AMap版本:', AMap.version);
      }
    });
  </script>


</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
