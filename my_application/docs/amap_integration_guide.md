# 高德地图集成指南

## 概述

本项目已成功集成高德地图 SDK，为中国地区用户提供优质的地图服务。高德地图在中国大陆地区具有更好的数据准确性和服务稳定性。

## 已集成的包

- `amap_flutter_map: ^3.0.0` - 高德地图显示组件
- `amap_flutter_location: ^3.0.0` - 高德定位服务
- `amap_flutter_base: ^3.0.0` - 高德地图基础组件

## 配置说明

### Android 配置

在 `android/app/src/main/AndroidManifest.xml` 中已添加：

1. **权限配置**：
   - 网络访问权限
   - 位置访问权限
   - 存储访问权限
   - WiFi 状态权限等

2. **API Key 配置**：
   ```xml
   <meta-data
       android:name="com.amap.api.v2.apikey"
       android:value="7e2b9a68a29a502de59bca3affb3c68d" />
   ```

### iOS 配置

在 `ios/Runner/Info.plist` 中已添加：

1. **定位权限描述**：
   - NSLocationWhenInUseUsageDescription
   - NSLocationAlwaysAndWhenInUseUsageDescription
   - NSLocationAlwaysUsageDescription

2. **API Key 配置**：
   ```xml
   <key>AMapApiKey</key>
   <string>7e2b9a68a29a502de59bca3affb3c68d</string>
   ```

## 核心文件说明

### 1. 配置文件
- `lib/constants/amap_config.dart` - 高德地图配置常量
- `lib/constants/app_constants.dart` - 应用常量（包含地图相关配置）

### 2. 服务类
- `lib/services/amap_service.dart` - 高德地图服务封装类

### 3. UI 组件
- `lib/widgets/amap_widget.dart` - 高德地图 Widget 组件

### 4. 示例页面
- `lib/examples/amap_example_page.dart` - 高德地图使用示例

## 使用方法

### 基本使用

```dart
import 'package:flutter/material.dart';
import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:amap_flutter_base/amap_flutter_base.dart';
import '../constants/amap_config.dart';

class MyMapPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('地图')),
      body: AMapWidget(
        apiKey: AMapApiKey(
          androidKey: AmapConfig.androidApiKey,
          iosKey: AmapConfig.iosApiKey,
        ),
        initialCameraPosition: CameraPosition(
          target: LatLng(39.9042, 116.4074),
          zoom: 16.0,
        ),
        onMapCreated: (AMapController controller) {
          // 地图创建完成
        },
        onTap: (LatLng position) {
          print('点击位置: ${position.latitude}, ${position.longitude}');
        },
      ),
    );
  }
}
```

### 获取当前位置

```dart
import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  AMapFlutterLocation? _locationPlugin;

  Future<void> initLocation() async {
    // 设置API Key
    AMapFlutterLocation.setApiKey(
      "your_android_key",
      "your_ios_key",
    );

    // 设置隐私政策
    AMapFlutterLocation.updatePrivacyShow(true, true);
    AMapFlutterLocation.updatePrivacyAgree(true);

    _locationPlugin = AMapFlutterLocation();
  }

  Future<Map<String, Object>?> getCurrentLocation() async {
    // 检查权限
    if (await Permission.location.request().isGranted) {
      // 开始定位
      _locationPlugin?.startLocation();

      // 监听定位结果
      return await _locationPlugin?.onLocationChanged().first;
    }
    return null;
  }
}
```

## 主要功能

1. **地图显示**
   - 支持标准地图、卫星地图等多种地图类型
   - 支持缩放、旋转、倾斜等手势操作
   - 支持自定义地图样式

2. **定位服务**
   - 高精度定位
   - 连续定位和单次定位
   - 逆地理编码（获取地址信息）

3. **标记和覆盖物**
   - 添加自定义标记
   - 绘制线条和多边形
   - 信息窗口显示

4. **权限管理**
   - 自动检查和请求定位权限
   - 权限状态监听

## 注意事项

1. **API Key 安全**
   - 当前使用的是测试 API Key
   - 生产环境请替换为正式的 API Key
   - 建议在高德开放平台设置包名和签名限制

2. **权限处理**
   - 首次使用需要用户授权定位权限
   - 建议在使用前检查权限状态

3. **网络要求**
   - 地图数据需要网络连接
   - 建议在网络异常时提供友好提示

4. **性能优化**
   - 避免频繁创建和销毁地图实例
   - 及时释放定位资源

## 测试方法

运行示例页面：

```dart
import 'package:flutter/material.dart';
import 'lib/examples/amap_example_page.dart';

void main() {
  runApp(MaterialApp(
    home: AmapExamplePage(),
  ));
}
```

## 常见问题

1. **地图不显示**
   - 检查 API Key 是否正确配置
   - 检查网络连接
   - 查看控制台错误信息

2. **定位失败**
   - 检查定位权限是否授权
   - 确认设备 GPS 功能已开启
   - 检查网络连接状态

3. **编译错误**
   - 运行 `flutter clean` 清理缓存
   - 运行 `flutter pub get` 重新获取依赖
   - 检查 Android/iOS 配置是否正确

## 更多资源

- [高德开放平台](https://lbs.amap.com/)
- [高德地图 Flutter 插件文档](https://pub.dev/packages/amap_flutter_map)
- [高德定位 Flutter 插件文档](https://pub.dev/packages/amap_flutter_location)
