# Heroicons Flutter 使用指南 - FavsAny

## 📦 安装

Heroicons 已经成功安装到项目中：

```yaml
dependencies:
  heroicons: ^0.11.0
```

## 🎯 基本使用

### 1. 导入包

```dart
import 'package:heroicons/heroicons.dart';
```

### 2. 基本语法

```dart
HeroIcon(
  HeroIcons.iconName,           // 图标名称
  style: HeroIconStyle.outline, // 图标样式
  color: Colors.blue,           // 图标颜色
  size: 24,                     // 图标大小
)
```

## 🎨 图标样式

Heroicons 提供三种样式：

### Outline (轮廓)
```dart
HeroIcon(
  HeroIcons.heart,
  style: HeroIconStyle.outline,
)
```

### Solid (实心)
```dart
HeroIcon(
  HeroIcons.heart,
  style: HeroIconStyle.solid,
)
```

### Mini (迷你)
```dart
HeroIcon(
  HeroIcons.heart,
  style: HeroIconStyle.mini,
)
```

## 🗺️ 在 FavsAny 中的应用

### 导航栏图标
```dart
// 菜单图标
HeroIcon(
  HeroIcons.bars3,
  style: HeroIconStyle.outline,
  color: AppColors.primaryLight70,
)

// 搜索图标
HeroIcon(
  HeroIcons.magnifyingGlass,
  style: HeroIconStyle.outline,
  color: AppColors.primaryLight70,
)

// 关闭图标
HeroIcon(
  HeroIcons.xMark,
  style: HeroIconStyle.outline,
  color: AppColors.primaryLight70,
)
```

### 地图相关图标
```dart
// 地图标记
HeroIcon(HeroIcons.mapPin, style: HeroIconStyle.outline)

// 添加位置
HeroIcon(HeroIcons.plus, style: HeroIconStyle.outline)

// 位置导航
HeroIcon(HeroIcons.map, style: HeroIconStyle.outline)
```

### 功能图标
```dart
// 收藏
HeroIcon(HeroIcons.heart, style: HeroIconStyle.outline)

// 分享
HeroIcon(HeroIcons.share, style: HeroIconStyle.outline)

// 设置
HeroIcon(HeroIcons.cog6Tooth, style: HeroIconStyle.outline)

// 信息
HeroIcon(HeroIcons.informationCircle, style: HeroIconStyle.outline)
```

## 🎯 常用图标列表

### 导航类
- `HeroIcons.bars3` - 菜单
- `HeroIcons.magnifyingGlass` - 搜索
- `HeroIcons.xMark` - 关闭/清除
- `HeroIcons.arrowLeft` - 返回
- `HeroIcons.arrowRight` - 前进

### 地图类
- `HeroIcons.mapPin` - 地图标记
- `HeroIcons.map` - 地图
- `HeroIcons.globeAlt` - 全球
- `HeroIcons.compass` - 指南针

### 功能类
- `HeroIcons.heart` - 收藏/喜欢
- `HeroIcons.plus` - 添加
- `HeroIcons.pencil` - 编辑
- `HeroIcons.trash` - 删除
- `HeroIcons.share` - 分享

### 设置类
- `HeroIcons.cog6Tooth` - 设置
- `HeroIcons.informationCircle` - 信息
- `HeroIcons.user` - 用户
- `HeroIcons.bell` - 通知
- `HeroIcons.lockClosed` - 安全

### 分类类
- `HeroIcons.buildingStorefront` - 商店
- `HeroIcons.cup` - 咖啡/饮品
- `HeroIcons.bookOpen` - 书籍/阅读
- `HeroIcons.shoppingBag` - 购物
- `HeroIcons.briefcase` - 工作

## 💡 最佳实践

### 1. 保持一致的样式
在整个应用中使用相同的图标样式（推荐 outline）：

```dart
// 推荐：统一使用 outline 样式
HeroIcon(
  HeroIcons.heart,
  style: HeroIconStyle.outline,
  color: AppColors.primaryLight70,
)
```

### 2. 使用应用主题色
结合应用的透明度橘红色主题：

```dart
HeroIcon(
  HeroIcons.mapPin,
  style: HeroIconStyle.outline,
  color: AppColors.primaryLight70, // 70% 透明度
)
```

### 3. 适当的图标大小
根据使用场景选择合适的大小：

```dart
// 导航栏图标
HeroIcon(HeroIcons.bars3, size: 24)

// 大型功能按钮
HeroIcon(HeroIcons.plus, size: 32)

// 列表项图标
HeroIcon(HeroIcons.heart, size: 20)
```

## 🔗 更多资源

- [Heroicons 官网](https://heroicons.com/)
- [Flutter Heroicons 包](https://pub.dev/packages/heroicons)
- [完整图标列表](https://heroicons.com/)

## 📝 示例代码

查看 `lib/examples/heroicons_example.dart` 文件获取完整的使用示例。
