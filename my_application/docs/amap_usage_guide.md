# 高德地图集成使用指南

## 概述

本项目已成功集成高德地图 Flutter SDK，包含以下功能：
- 地图显示
- 定位服务
- 标记添加
- 权限管理

## 已添加的依赖

```yaml
dependencies:
  amap_flutter_map: ^3.0.0      # 高德地图显示
  amap_flutter_location: ^3.0.0 # 高德定位服务
  amap_flutter_base: ^3.0.0     # 高德基础库
```

## 配置文件

### Android 配置
文件：`android/app/src/main/AndroidManifest.xml`
- 已添加必要的权限
- 已配置 API Key

### iOS 配置
文件：`ios/Runner/Info.plist`
- 已添加位置权限描述
- 已配置 API Key

## 核心文件

### 1. 配置文件
- `lib/constants/amap_config.dart` - 高德地图配置常量

### 2. 服务类
- `lib/services/simple_amap_service.dart` - 简化的高德地图服务

### 3. 示例页面
- `lib/examples/simple_amap_example.dart` - 完整的使用示例

## 快速开始

### 1. 基本地图显示

```dart
import 'package:flutter/material.dart';
import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:amap_flutter_base/amap_flutter_base.dart';
import '../constants/amap_config.dart';

class MyMapPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('地图')),
      body: AMapWidget(
        apiKey: AMapApiKey(
          androidKey: AmapConfig.androidApiKey,
          iosKey: AmapConfig.iosApiKey,
        ),
        initialCameraPosition: CameraPosition(
          target: LatLng(39.9042, 116.4074), // 北京天安门
          zoom: 16.0,
        ),
        onMapCreated: (AMapController controller) {
          // 地图创建完成回调
        },
        onTap: (LatLng position) {
          print('点击位置: ${position.latitude}, ${position.longitude}');
        },
      ),
    );
  }
}
```

### 2. 获取当前位置

```dart
import '../services/simple_amap_service.dart';

class LocationExample extends StatefulWidget {
  @override
  _LocationExampleState createState() => _LocationExampleState();
}

class _LocationExampleState extends State<LocationExample> {
  final _amapService = SimpleAmapService();
  String _locationInfo = '点击按钮获取位置';

  Future<void> _getCurrentLocation() async {
    try {
      final location = await _amapService.getCurrentLocation();
      if (location != null && SimpleAmapService.isLocationValid(location)) {
        setState(() {
          _locationInfo = SimpleAmapService.formatLocationInfo(location);
        });
      }
    } catch (e) {
      setState(() {
        _locationInfo = '定位失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('定位示例')),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: _getCurrentLocation,
            child: Text('获取当前位置'),
          ),
          Padding(
            padding: EdgeInsets.all(16),
            child: Text(_locationInfo),
          ),
        ],
      ),
    );
  }
}
```

### 3. 运行示例

要查看完整的示例，可以运行：

```dart
import '../examples/simple_amap_example.dart';

// 在你的路由中添加
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => SimpleAmapExample()),
);
```

## API 说明

### SimpleAmapService 主要方法

- `initialize()` - 初始化服务
- `getCurrentLocation()` - 获取单次定位
- `startLocationStream()` - 开始连续定位
- `stopLocation()` - 停止定位
- `checkAndRequestLocationPermission()` - 检查权限

### 静态工具方法

- `getLatitude(location)` - 获取纬度
- `getLongitude(location)` - 获取经度
- `getAddress(location)` - 获取地址
- `isLocationValid(location)` - 检查位置有效性
- `formatLocationInfo(location)` - 格式化位置信息

## 注意事项

1. **权限处理**：应用会自动请求定位权限，但用户可能拒绝
2. **API Key**：确保在 `amap_config.dart` 中配置了正确的 API Key
3. **网络连接**：地图加载需要网络连接
4. **隐私政策**：已按照高德要求设置隐私政策同意

## 常见问题

### Q: 地图不显示怎么办？
A: 检查 API Key 是否正确配置，网络是否正常

### Q: 定位失败怎么办？
A: 检查权限是否授予，GPS 是否开启

### Q: 如何自定义地图样式？
A: 可以在 `AmapConfig` 中修改默认配置

## 下一步

- 添加更多地图功能（路径规划、搜索等）
- 集成到现有的 FavsAny 应用中
- 添加自定义标记样式
- 实现离线地图功能
