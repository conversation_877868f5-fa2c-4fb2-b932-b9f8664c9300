import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'dart:math' as math;

/// 下拉刷新状态
enum RefreshState {
  idle,
  pulling,
  readyToRefresh,
  refreshing,
  completed,
}

/// 自定义下拉刷新组件
class CustomPullToRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;
  final double maxDistance;
  final Duration animationDuration;
  final Color? indicatorColor;
  final Color? backgroundColor;
  final String? refreshText;
  final String? releaseText;
  final String? refreshingText;
  final String? completedText;

  const CustomPullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 80,
    this.maxDistance = 120,
    this.animationDuration = const Duration(milliseconds: 300),
    this.indicatorColor,
    this.backgroundColor,
    this.refreshText = '下拉刷新',
    this.releaseText = '释放刷新',
    this.refreshingText = '正在刷新...',
    this.completedText = '刷新完成',
  });

  @override
  State<CustomPullToRefresh> createState() => _CustomPullToRefreshState();
}

class _CustomPullToRefreshState extends State<CustomPullToRefresh>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late Animation<double> _animation;
  late Animation<double> _rotationAnimation;
  
  RefreshState _refreshState = RefreshState.idle;
  double _dragOffset = 0;
  bool _dragInProgress = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollStartNotification) {
      _dragInProgress = true;
    } else if (notification is ScrollUpdateNotification) {
      if (_dragInProgress && notification.metrics.pixels <= 0) {
        final offset = -notification.metrics.pixels;
        _updateDragOffset(offset);
      }
    } else if (notification is ScrollEndNotification) {
      _handleDragEnd();
    }
    return false;
  }

  void _updateDragOffset(double offset) {
    setState(() {
      _dragOffset = math.min(offset, widget.maxDistance);
      
      if (_refreshState == RefreshState.refreshing) {
        return;
      }
      
      if (_dragOffset >= widget.triggerDistance) {
        if (_refreshState != RefreshState.readyToRefresh) {
          _refreshState = RefreshState.readyToRefresh;
          // 触觉反馈
          // HapticFeedback.lightImpact();
        }
      } else if (_dragOffset > 0) {
        _refreshState = RefreshState.pulling;
      } else {
        _refreshState = RefreshState.idle;
      }
    });
  }

  void _handleDragEnd() {
    _dragInProgress = false;
    
    if (_refreshState == RefreshState.readyToRefresh) {
      _startRefresh();
    } else {
      _resetToIdle();
    }
  }

  void _startRefresh() async {
    setState(() {
      _refreshState = RefreshState.refreshing;
    });
    
    _rotationController.repeat();
    
    try {
      await widget.onRefresh();
      
      setState(() {
        _refreshState = RefreshState.completed;
      });
      
      // 显示完成状态一段时间
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      // 处理错误
      debugPrint('Refresh error: $e');
    } finally {
      _rotationController.stop();
      _resetToIdle();
    }
  }

  void _resetToIdle() {
    _animationController.reverse().then((_) {
      setState(() {
        _refreshState = RefreshState.idle;
        _dragOffset = 0;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: _handleScrollNotification,
      child: Stack(
        children: [
          // 刷新指示器
          if (_dragOffset > 0) _buildRefreshIndicator(),
          
          // 主要内容
          Transform.translate(
            offset: Offset(0, _getContentOffset()),
            child: widget.child,
          ),
        ],
      ),
    );
  }

  double _getContentOffset() {
    if (_refreshState == RefreshState.refreshing) {
      return widget.triggerDistance;
    }
    return _dragOffset;
  }

  Widget _buildRefreshIndicator() {
    final progress = (_dragOffset / widget.triggerDistance).clamp(0.0, 1.0);
    final indicatorColor = widget.indicatorColor ?? Theme.of(context).primaryColor;
    final backgroundColor = widget.backgroundColor ?? Colors.white;
    
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      height: _dragOffset,
      child: Container(
        color: backgroundColor,
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 图标
                _buildIndicatorIcon(progress, indicatorColor),
                
                const SizedBox(height: 8),
                
                // 文字
                Text(
                  _getIndicatorText(),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorIcon(double progress, Color color) {
    switch (_refreshState) {
      case RefreshState.idle:
      case RefreshState.pulling:
        return Transform.rotate(
          angle: progress * math.pi,
          child: HeroIcon(
            HeroIcons.arrowDown,
            style: HeroIconStyle.outline,
            size: 20,
            color: color.withValues(alpha: progress),
          ),
        );
        
      case RefreshState.readyToRefresh:
        return HeroIcon(
          HeroIcons.arrowUp,
          style: HeroIconStyle.outline,
          size: 20,
          color: color,
        );
        
      case RefreshState.refreshing:
        return AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value,
              child: HeroIcon(
                HeroIcons.arrowPath,
                style: HeroIconStyle.outline,
                size: 20,
                color: color,
              ),
            );
          },
        );
        
      case RefreshState.completed:
        return HeroIcon(
          HeroIcons.checkCircle,
          style: HeroIconStyle.solid,
          size: 20,
          color: Colors.green,
        );
    }
  }

  String _getIndicatorText() {
    switch (_refreshState) {
      case RefreshState.idle:
      case RefreshState.pulling:
        return widget.refreshText ?? '下拉刷新';
        
      case RefreshState.readyToRefresh:
        return widget.releaseText ?? '释放刷新';
        
      case RefreshState.refreshing:
        return widget.refreshingText ?? '正在刷新...';
        
      case RefreshState.completed:
        return widget.completedText ?? '刷新完成';
    }
  }
}

/// 简化的下拉刷新包装器
class SimplePullToRefresh extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final Color? color;

  const SimplePullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      color: color ?? Theme.of(context).primaryColor,
      backgroundColor: Colors.white,
      strokeWidth: 2,
      displacement: 40,
      child: child,
    );
  }
}

/// 带有空状态的下拉刷新列表
class RefreshableList extends StatelessWidget {
  final List<Widget> children;
  final Future<void> Function() onRefresh;
  final Widget? emptyWidget;
  final String? emptyText;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;

  const RefreshableList({
    super.key,
    required this.children,
    required this.onRefresh,
    this.emptyWidget,
    this.emptyText,
    this.padding,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    if (children.isEmpty) {
      return CustomPullToRefresh(
        onRefresh: onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Center(
              child: emptyWidget ?? _buildDefaultEmptyWidget(),
            ),
          ),
        ),
      );
    }

    return CustomPullToRefresh(
      onRefresh: onRefresh,
      child: ListView(
        padding: padding,
        physics: physics ?? const AlwaysScrollableScrollPhysics(),
        children: children,
      ),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        HeroIcon(
          HeroIcons.folderOpen,
          style: HeroIconStyle.outline,
          size: 48,
          color: Colors.grey[400],
        ),
        const SizedBox(height: 16),
        Text(
          emptyText ?? '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '下拉刷新试试',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }
}
