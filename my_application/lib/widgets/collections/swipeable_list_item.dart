import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'dart:math' as math;

/// 滑动操作项
class SwipeAction {
  final String id;
  final String label;
  final HeroIcons icon;
  final Color backgroundColor;
  final Color foregroundColor;
  final VoidCallback onPressed;

  const SwipeAction({
    required this.id,
    required this.label,
    required this.icon,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.onPressed,
  });
}

/// 可滑动的列表项
class SwipeableListItem extends StatefulWidget {
  final Widget child;
  final List<SwipeAction> leftActions;
  final List<SwipeAction> rightActions;
  final double actionWidth;
  final Duration animationDuration;
  final Curve animationCurve;
  final double threshold;

  const SwipeableListItem({
    super.key,
    required this.child,
    this.leftActions = const [],
    this.rightActions = const [],
    this.actionWidth = 80,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.threshold = 0.3,
  });

  @override
  State<SwipeableListItem> createState() => _SwipeableListItemState();
}

class _SwipeableListItemState extends State<SwipeableListItem>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  double _dragExtent = 0;
  bool _dragUnderway = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleDragStart(DragStartDetails details) {
    _dragUnderway = true;
    if (_animationController.isAnimating) {
      _animationController.stop();
    }
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_dragUnderway) return;

    final delta = details.primaryDelta!;
    final oldDragExtent = _dragExtent;
    
    setState(() {
      _dragExtent += delta;
      
      // 限制滑动范围
      final maxLeftExtent = widget.leftActions.length * widget.actionWidth;
      final maxRightExtent = widget.rightActions.length * widget.actionWidth;
      
      if (_dragExtent > maxLeftExtent) {
        _dragExtent = maxLeftExtent;
      } else if (_dragExtent < -maxRightExtent) {
        _dragExtent = -maxRightExtent;
      }
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_dragUnderway) return;
    _dragUnderway = false;

    final velocity = details.primaryVelocity ?? 0;
    final width = context.size?.width ?? 0;
    
    // 根据速度和位置决定是否展开操作
    bool shouldExpand = false;
    
    if (velocity.abs() > 300) {
      // 快速滑动
      shouldExpand = velocity > 0 ? _dragExtent > 0 : _dragExtent < 0;
    } else {
      // 慢速滑动，根据阈值判断
      final threshold = width * widget.threshold;
      shouldExpand = _dragExtent.abs() > threshold;
    }

    if (shouldExpand) {
      _expandToActions();
    } else {
      _resetPosition();
    }
  }

  void _expandToActions() {
    final targetExtent = _dragExtent > 0
        ? widget.leftActions.length * widget.actionWidth
        : -widget.rightActions.length * widget.actionWidth;
    
    _animateToPosition(targetExtent);
  }

  void _resetPosition() {
    _animateToPosition(0);
  }

  void _animateToPosition(double targetExtent) {
    final startExtent = _dragExtent;
    final distance = targetExtent - startExtent;
    
    _animationController.reset();
    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    ));
    
    _animation.addListener(() {
      setState(() {
        _dragExtent = startExtent + (distance * _animation.value);
      });
    });
    
    _animationController.forward();
  }

  void _handleActionTap(SwipeAction action) {
    action.onPressed();
    _resetPosition();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragStart: _handleDragStart,
      onHorizontalDragUpdate: _handleDragUpdate,
      onHorizontalDragEnd: _handleDragEnd,
      child: Stack(
        children: [
          // 背景操作按钮
          if (_dragExtent != 0) _buildActionBackground(),
          
          // 主要内容
          Transform.translate(
            offset: Offset(_dragExtent, 0),
            child: widget.child,
          ),
        ],
      ),
    );
  }

  Widget _buildActionBackground() {
    if (_dragExtent > 0) {
      // 左滑显示左侧操作
      return _buildLeftActions();
    } else {
      // 右滑显示右侧操作
      return _buildRightActions();
    }
  }

  Widget _buildLeftActions() {
    return Positioned(
      left: 0,
      top: 0,
      bottom: 0,
      width: _dragExtent,
      child: Row(
        children: widget.leftActions.map((action) {
          return _buildActionButton(action);
        }).toList(),
      ),
    );
  }

  Widget _buildRightActions() {
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      width: -_dragExtent,
      child: Row(
        children: widget.rightActions.map((action) {
          return _buildActionButton(action);
        }).toList(),
      ),
    );
  }

  Widget _buildActionButton(SwipeAction action) {
    return Expanded(
      child: Container(
        color: action.backgroundColor,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _handleActionTap(action),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                HeroIcon(
                  action.icon,
                  style: HeroIconStyle.outline,
                  size: 20,
                  color: action.foregroundColor,
                ),
                const SizedBox(height: 4),
                Text(
                  action.label,
                  style: TextStyle(
                    fontSize: 12,
                    color: action.foregroundColor,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 预定义的滑动操作
class SwipeActions {
  static SwipeAction edit({required VoidCallback onPressed}) {
    return SwipeAction(
      id: 'edit',
      label: '编辑',
      icon: HeroIcons.pencil,
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction delete({required VoidCallback onPressed}) {
    return SwipeAction(
      id: 'delete',
      label: '删除',
      icon: HeroIcons.trash,
      backgroundColor: Colors.red,
      foregroundColor: Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction share({required VoidCallback onPressed}) {
    return SwipeAction(
      id: 'share',
      label: '分享',
      icon: HeroIcons.share,
      backgroundColor: Colors.green,
      foregroundColor: Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction favorite({required VoidCallback onPressed}) {
    return SwipeAction(
      id: 'favorite',
      label: '收藏',
      icon: HeroIcons.heart,
      backgroundColor: Colors.pink,
      foregroundColor: Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction archive({required VoidCallback onPressed}) {
    return SwipeAction(
      id: 'archive',
      label: '归档',
      icon: HeroIcons.archiveBox,
      backgroundColor: Colors.orange,
      foregroundColor: Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction pin({required VoidCallback onPressed}) {
    return SwipeAction(
      id: 'pin',
      label: '置顶',
      icon: HeroIcons.bookmark,
      backgroundColor: Colors.purple,
      foregroundColor: Colors.white,
      onPressed: onPressed,
    );
  }
}

/// 带有滑动操作的收藏项卡片
class SwipeableMarkerCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;
  final VoidCallback? onFavorite;

  const SwipeableMarkerCard({
    super.key,
    required this.child,
    this.onEdit,
    this.onDelete,
    this.onShare,
    this.onFavorite,
  });

  @override
  Widget build(BuildContext context) {
    final leftActions = <SwipeAction>[];
    final rightActions = <SwipeAction>[];

    // 左侧操作（正面操作）
    if (onEdit != null) {
      leftActions.add(SwipeActions.edit(onPressed: onEdit!));
    }
    if (onShare != null) {
      leftActions.add(SwipeActions.share(onPressed: onShare!));
    }

    // 右侧操作（危险操作）
    if (onFavorite != null) {
      rightActions.add(SwipeActions.favorite(onPressed: onFavorite!));
    }
    if (onDelete != null) {
      rightActions.add(SwipeActions.delete(onPressed: onDelete!));
    }

    return SwipeableListItem(
      leftActions: leftActions,
      rightActions: rightActions,
      child: child,
    );
  }
}
