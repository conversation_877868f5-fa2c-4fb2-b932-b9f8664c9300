import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import '../models/marker_model.dart';
import '../constants/app_colors.dart';

class MarkerListItem extends StatelessWidget {
  final MarkerModel marker;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final Function(bool?) onSelectionChanged;

  const MarkerListItem({
    super.key,
    required this.marker,
    required this.isSelected,
    required this.isSelectionMode,
    required this.onTap,
    required this.onLongPress,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected 
                ? Border.all(color: AppColors.primary, width: 2)
                : null,
          ),
          child: Row(
            children: [
              // 选择框或缩略图
              if (isSelectionMode)
                Checkbox(
                  value: isSelected,
                  onChanged: onSelectionChanged,
                )
              else
                _buildThumbnail(),
              
              const SizedBox(width: 12),
              
              // 内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和分类
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            marker.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildCategoryChip(),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // 描述
                    if (marker.description != null && marker.description!.isNotEmpty)
                      Text(
                        marker.description!,
                        style: const TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    
                    const SizedBox(height: 8),
                    
                    // 底部信息
                    Row(
                      children: [
                        // 评分
                        if (marker.rating != null) ...[
                          Row(
                            children: List.generate(5, (index) {
                              return Icon(
                                index < marker.rating! ? Icons.star : Icons.star_border,
                                size: 14,
                                color: Colors.amber,
                              );
                            }),
                          ),
                          const SizedBox(width: 8),
                        ],
                        
                        // 创建时间
                        Text(
                          DateFormat('MM/dd').format(marker.createdAt),
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                        
                        const Spacer(),
                        
                        // 状态图标
                        Row(
                          children: [
                            if (marker.imagePaths.isNotEmpty)
                              const Icon(
                                Icons.image,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                            if (marker.imagePaths.isNotEmpty && marker.isPublic)
                              const SizedBox(width: 4),
                            if (marker.isPublic)
                              const Icon(
                                Icons.public,
                                size: 16,
                                color: AppColors.success,
                              ),
                          ],
                        ),
                      ],
                    ),
                    
                    // 标签
                    if (marker.tags.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: marker.tags.take(3).map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '#$tag',
                              style: const TextStyle(
                                fontSize: 10,
                                color: AppColors.primary,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThumbnail() {
    if (marker.imagePaths.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: _buildImage(marker.imagePaths.first),
      );
    } else {
      return Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppColors.getCategoryColor(marker.category).withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          _getCategoryIcon(marker.category),
          color: AppColors.getCategoryColor(marker.category),
          size: 24,
        ),
      );
    }
  }

  Widget _buildImage(String imagePath) {
    if (imagePath.startsWith('http')) {
      return Image.network(
        imagePath,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorImage(),
      );
    } else {
      return Image.file(
        File(imagePath),
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorImage(),
      );
    }
  }

  Widget _buildErrorImage() {
    return Container(
      width: 60,
      height: 60,
      color: Colors.grey[200],
      child: const Icon(
        Icons.broken_image,
        color: Colors.grey,
      ),
    );
  }

  Widget _buildCategoryChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.getCategoryColor(marker.category).withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        marker.category,
        style: TextStyle(
          color: AppColors.getCategoryColor(marker.category),
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '餐厅':
        return Icons.restaurant;
      case '咖啡馆':
        return Icons.local_cafe;
      case '书店':
        return Icons.menu_book;
      case '景点':
        return Icons.place;
      case '购物':
        return Icons.shopping_bag;
      case '工作':
        return Icons.work;
      case '旅行':
        return Icons.flight;
      default:
        return Icons.place;
    }
  }
}
