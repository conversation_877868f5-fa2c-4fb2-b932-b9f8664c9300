import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'dart:ui';

/// 底部标签栏项目
class BottomTabItem {
  final String id;
  final String label;
  final HeroIcons icon;
  final HeroIcons activeIcon;
  final Color? color;

  const BottomTabItem({
    required this.id,
    required this.label,
    required this.icon,
    HeroIcons? activeIcon,
    this.color,
  }) : activeIcon = activeIcon ?? icon;
}

/// 底部标签栏
class CustomBottomTabBar extends StatelessWidget {
  final List<BottomTabItem> items;
  final String currentTab;
  final Function(String) onTabChanged;
  final bool showLabels;
  final double height;
  final Color? backgroundColor;
  final double blurRadius;
  final double opacity;

  const CustomBottomTabBar({
    super.key,
    required this.items,
    required this.currentTab,
    required this.onTabChanged,
    this.showLabels = true,
    this.height = 80,
    this.backgroundColor,
    this.blurRadius = 20,
    this.opacity = 0.8,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blurRadius, sigmaY: blurRadius),
          child: Container(
            decoration: BoxDecoration(
              color: (backgroundColor ?? Colors.white).withValues(alpha: opacity),
              border: Border(
                top: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 0.5,
                ),
              ),
            ),
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: items.map((item) {
                    final isActive = currentTab == item.id;
                    return _buildTabItem(item, isActive);
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabItem(BottomTabItem item, bool isActive) {
    final color = item.color ?? (isActive ? Colors.blue[600] : Colors.grey[600]);
    
    return Expanded(
      child: GestureDetector(
        onTap: () => onTabChanged(item.id),
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 图标容器
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isActive 
                      ? color!.withValues(alpha: 0.15)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: HeroIcon(
                  isActive ? item.activeIcon : item.icon,
                  style: isActive ? HeroIconStyle.solid : HeroIconStyle.outline,
                  size: 24,
                  color: color,
                ),
              ),
              
              // 标签文字
              if (showLabels) ...[
                const SizedBox(height: 4),
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 200),
                  style: TextStyle(
                    fontSize: isActive ? 12 : 11,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                    color: color,
                  ),
                  child: Text(
                    item.label,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// 浮动操作按钮底部标签栏
class FloatingBottomTabBar extends StatelessWidget {
  final List<BottomTabItem> items;
  final String currentTab;
  final Function(String) onTabChanged;
  final VoidCallback? onCenterButtonPressed;
  final Widget? centerButton;
  final double margin;
  final double borderRadius;

  const FloatingBottomTabBar({
    super.key,
    required this.items,
    required this.currentTab,
    required this.onTabChanged,
    this.onCenterButtonPressed,
    this.centerButton,
    this.margin = 16,
    this.borderRadius = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(margin),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 底部标签栏
          Container(
            height: 64,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(borderRadius),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(borderRadius),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.2),
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    children: [
                      // 左侧标签
                      ...items.take(items.length ~/ 2).map((item) {
                        final isActive = currentTab == item.id;
                        return Expanded(
                          child: _buildFloatingTabItem(item, isActive),
                        );
                      }),
                      
                      // 中间空间（为浮动按钮留位置）
                      const SizedBox(width: 64),
                      
                      // 右侧标签
                      ...items.skip(items.length ~/ 2).map((item) {
                        final isActive = currentTab == item.id;
                        return Expanded(
                          child: _buildFloatingTabItem(item, isActive),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // 中心浮动按钮
          if (centerButton != null || onCenterButtonPressed != null)
            Positioned(
              child: centerButton ?? _buildDefaultCenterButton(),
            ),
        ],
      ),
    );
  }

  Widget _buildFloatingTabItem(BottomTabItem item, bool isActive) {
    final color = item.color ?? (isActive ? Colors.blue[600] : Colors.grey[600]);
    
    return GestureDetector(
      onTap: () => onTabChanged(item.id),
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeroIcon(
              isActive ? item.activeIcon : item.icon,
              style: isActive ? HeroIconStyle.solid : HeroIconStyle.outline,
              size: 20,
              color: color,
            ),
            const SizedBox(height: 4),
            Text(
              item.label,
              style: TextStyle(
                fontSize: 10,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                color: color,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultCenterButton() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[400]!, Colors.blue[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onCenterButtonPressed,
          borderRadius: BorderRadius.circular(28),
          child: const Center(
            child: HeroIcon(
              HeroIcons.plus,
              style: HeroIconStyle.outline,
              size: 24,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

/// 预定义的标签项目
class TabItems {
  static const map = BottomTabItem(
    id: 'map',
    label: '地图',
    icon: HeroIcons.map,
    activeIcon: HeroIcons.map,
    color: Colors.blue,
  );

  static const favorites = BottomTabItem(
    id: 'favorites',
    label: '收藏',
    icon: HeroIcons.heart,
    activeIcon: HeroIcons.heart,
    color: Colors.red,
  );

  static const search = BottomTabItem(
    id: 'search',
    label: '搜索',
    icon: HeroIcons.magnifyingGlass,
    activeIcon: HeroIcons.magnifyingGlass,
    color: Colors.green,
  );

  static const profile = BottomTabItem(
    id: 'profile',
    label: '我的',
    icon: HeroIcons.user,
    activeIcon: HeroIcons.user,
    color: Colors.purple,
  );

  static const settings = BottomTabItem(
    id: 'settings',
    label: '设置',
    icon: HeroIcons.cog6Tooth,
    activeIcon: HeroIcons.cog6Tooth,
    color: Colors.grey,
  );

  static const List<BottomTabItem> defaultTabs = [
    map,
    favorites,
    search,
    profile,
  ];

  static const List<BottomTabItem> mainTabs = [
    map,
    favorites,
  ];
}
