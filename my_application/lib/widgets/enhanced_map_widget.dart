import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:heroicons/heroicons.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:math' as math;

import '../models/marker_model.dart';
import '../services/location_service.dart';

/// 增强的地图组件
/// 包含位置按钮、标记聚类、改进的地图控件和更好的用户位置显示
class EnhancedMapWidget extends StatefulWidget {
  final Function(GoogleMapController)? onMapCreated;
  final Set<Marker>? markers;
  final CameraPosition? initialCameraPosition;
  final bool myLocationEnabled;
  final bool showLocationButton;
  final bool enableMarkerClustering;
  final Function(LatLng)? onMapTap;
  final Function(LatLng)? onMapLongPress;
  final Function(Marker)? onMarkerTap;
  final VoidCallback? onLocationButtonPressed;
  final List<MarkerModel>? markerModels;

  const EnhancedMapWidget({
    super.key,
    this.onMapCreated,
    this.markers,
    this.initialCameraPosition,
    this.myLocationEnabled = true,
    this.showLocationButton = true,
    this.enableMarkerClustering = true,
    this.onMapTap,
    this.onMapLongPress,
    this.onMarkerTap,
    this.onLocationButtonPressed,
    this.markerModels,
  });

  @override
  State<EnhancedMapWidget> createState() => _EnhancedMapWidgetState();
}

class _EnhancedMapWidgetState extends State<EnhancedMapWidget>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  final LocationService _locationService = LocationService();
  
  LatLng? _currentLocation;
  bool _isLocationLoading = false;
  bool _isFollowingUser = false;
  double _currentZoom = 14.0;
  
  late AnimationController _locationButtonController;
  late AnimationController _zoomController;
  late Animation<double> _locationButtonAnimation;
  late Animation<double> _zoomAnimation;

  Set<Marker> _clusteredMarkers = {};
  Timer? _clusteringTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _getCurrentLocation();
    
    if (widget.enableMarkerClustering && widget.markerModels != null) {
      _updateMarkerClusters();
    }
  }

  @override
  void dispose() {
    _locationButtonController.dispose();
    _zoomController.dispose();
    _clusteringTimer?.cancel();
    super.dispose();
  }

  void _initializeAnimations() {
    _locationButtonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _zoomController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _locationButtonAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _locationButtonController,
      curve: Curves.elasticOut,
    ));
    
    _zoomAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _zoomController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _getCurrentLocation() async {
    if (!widget.myLocationEnabled) return;
    
    setState(() {
      _isLocationLoading = true;
    });
    
    try {
      final position = await _locationService.getCurrentPosition();
      final location = LatLng(position.latitude, position.longitude);
      
      setState(() {
        _currentLocation = location;
        _isLocationLoading = false;
      });
      
      _locationButtonController.forward();
      
    } catch (e) {
      setState(() {
        _isLocationLoading = false;
      });
      print('获取位置失败: $e');
    }
  }

  Future<void> _moveToCurrentLocation() async {
    if (_mapController == null) return;
    
    if (_currentLocation == null) {
      await _getCurrentLocation();
    }
    
    if (_currentLocation != null) {
      setState(() {
        _isFollowingUser = true;
      });
      
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_currentLocation!, 16.0),
      );
      
      // 触觉反馈
      // HapticFeedback.lightImpact();
      
      // 动画效果
      _zoomController.forward().then((_) {
        _zoomController.reverse();
      });
      
      if (widget.onLocationButtonPressed != null) {
        widget.onLocationButtonPressed!();
      }
    }
  }

  void _updateMarkerClusters() {
    if (!widget.enableMarkerClustering || widget.markerModels == null) {
      setState(() {
        _clusteredMarkers = widget.markers ?? {};
      });
      return;
    }
    
    _clusteringTimer?.cancel();
    _clusteringTimer = Timer(const Duration(milliseconds: 300), () {
      _performMarkerClustering();
    });
  }

  void _performMarkerClustering() {
    if (widget.markerModels == null) return;
    
    final clusters = <MarkerCluster>[];
    final processedMarkers = <MarkerModel>[];
    
    for (final marker in widget.markerModels!) {
      if (processedMarkers.contains(marker)) continue;
      
      final cluster = MarkerCluster(
        center: LatLng(marker.latitude, marker.longitude),
        markers: [marker],
      );
      
      // 查找附近的标记
      for (final otherMarker in widget.markerModels!) {
        if (otherMarker == marker || processedMarkers.contains(otherMarker)) continue;
        
        final distance = _calculateDistance(
          marker.latitude, marker.longitude,
          otherMarker.latitude, otherMarker.longitude,
        );
        
        // 如果距离小于聚类阈值（例如100米），则加入聚类
        if (distance < 100) {
          cluster.markers.add(otherMarker);
          processedMarkers.add(otherMarker);
        }
      }
      
      processedMarkers.add(marker);
      clusters.add(cluster);
    }
    
    // 生成聚类标记
    final clusteredMarkers = <Marker>{};
    
    for (int i = 0; i < clusters.length; i++) {
      final cluster = clusters[i];
      
      if (cluster.markers.length == 1) {
        // 单个标记
        final marker = cluster.markers.first;
        clusteredMarkers.add(
          Marker(
            markerId: MarkerId('marker_${marker.id}'),
            position: LatLng(marker.latitude, marker.longitude),
            infoWindow: InfoWindow(
              title: marker.title,
              snippet: marker.description,
            ),
            onTap: () {
              if (widget.onMarkerTap != null) {
                widget.onMarkerTap!(Marker(
                  markerId: MarkerId('marker_${marker.id}'),
                  position: LatLng(marker.latitude, marker.longitude),
                ));
              }
            },
          ),
        );
      } else {
        // 聚类标记
        clusteredMarkers.add(
          Marker(
            markerId: MarkerId('cluster_$i'),
            position: cluster.center,
            icon: await _createClusterIcon(cluster.markers.length),
            infoWindow: InfoWindow(
              title: '${cluster.markers.length} 个地点',
              snippet: '点击查看详情',
            ),
            onTap: () {
              _showClusterDialog(cluster.markers);
            },
          ),
        );
      }
    }
    
    setState(() {
      _clusteredMarkers = clusteredMarkers;
    });
  }

  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  Future<BitmapDescriptor> _createClusterIcon(int count) async {
    // 创建聚类图标
    // 这里可以使用自定义图标或者默认图标
    return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
  }

  void _showClusterDialog(List<MarkerModel> markers) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${markers.length} 个地点'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: markers.length,
            itemBuilder: (context, index) {
              final marker = markers[index];
              return ListTile(
                title: Text(marker.title),
                subtitle: Text(marker.description ?? ''),
                onTap: () {
                  Navigator.of(context).pop();
                  _moveToMarker(marker);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _moveToMarker(MarkerModel marker) async {
    if (_mapController == null) return;
    
    await _mapController!.animateCamera(
      CameraUpdate.newLatLngZoom(
        LatLng(marker.latitude, marker.longitude),
        16.0,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 地图主体
        GoogleMap(
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
            if (widget.onMapCreated != null) {
              widget.onMapCreated!(controller);
            }
          },
          initialCameraPosition: widget.initialCameraPosition ??
              const CameraPosition(
                target: LatLng(39.9042, 116.4074),
                zoom: 14.0,
              ),
          markers: widget.enableMarkerClustering ? _clusteredMarkers : (widget.markers ?? {}),
          myLocationEnabled: widget.myLocationEnabled,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          rotateGesturesEnabled: true,
          scrollGesturesEnabled: true,
          tiltGesturesEnabled: true,
          zoomGesturesEnabled: true,
          onTap: (LatLng position) {
            setState(() {
              _isFollowingUser = false;
            });
            if (widget.onMapTap != null) {
              widget.onMapTap!(position);
            }
          },
          onLongPress: widget.onMapLongPress,
          onCameraMove: (CameraPosition position) {
            _currentZoom = position.zoom;
            setState(() {
              _isFollowingUser = false;
            });
          },
          onCameraIdle: () {
            if (widget.enableMarkerClustering) {
              _updateMarkerClusters();
            }
          },
        ),
        
        // 地图控件
        _buildMapControls(),
      ],
    );
  }

  Widget _buildMapControls() {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 缩放控件
          _buildZoomControls(),
          
          const SizedBox(height: 8),
          
          // 位置按钮
          if (widget.showLocationButton) _buildLocationButton(),
        ],
      ),
    );
  }

  Widget _buildZoomControls() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _zoomIn,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              child: Container(
                width: 40,
                height: 40,
                child: const HeroIcon(
                  HeroIcons.plus,
                  style: HeroIconStyle.outline,
                  size: 20,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          Container(
            width: 40,
            height: 1,
            color: Colors.grey[300],
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _zoomOut,
              borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              child: Container(
                width: 40,
                height: 40,
                child: const HeroIcon(
                  HeroIcons.minus,
                  style: HeroIconStyle.outline,
                  size: 20,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationButton() {
    return AnimatedBuilder(
      animation: _locationButtonAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _locationButtonAnimation.value,
          child: AnimatedBuilder(
            animation: _zoomAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _zoomAnimation.value,
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _isFollowingUser ? Colors.blue : Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: _isLocationLoading ? null : _moveToCurrentLocation,
                      borderRadius: BorderRadius.circular(24),
                      child: Center(
                        child: _isLocationLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    _isFollowingUser ? Colors.white : Colors.blue,
                                  ),
                                ),
                              )
                            : HeroIcon(
                                _isFollowingUser ? HeroIcons.mapPin : HeroIcons.mapPin,
                                style: HeroIconStyle.solid,
                                size: 20,
                                color: _isFollowingUser ? Colors.white : Colors.blue,
                              ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _zoomIn() async {
    if (_mapController != null) {
      await _mapController!.animateCamera(CameraUpdate.zoomIn());
    }
  }

  void _zoomOut() async {
    if (_mapController != null) {
      await _mapController!.animateCamera(CameraUpdate.zoomOut());
    }
  }
}

/// 标记聚类类
class MarkerCluster {
  final LatLng center;
  final List<MarkerModel> markers;

  MarkerCluster({
    required this.center,
    required this.markers,
  });
}
