import 'package:flutter/material.dart';

/// 高德地图静态地图组件
/// 使用高德静态地图API实现，避免WebView兼容性问题
class AmapStaticWidget extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  final double zoom;
  final bool myLocationEnabled;
  final VoidCallback? onMyLocationButtonPressed;
  final Function(double lat, double lng)? onMapTap;

  const AmapStaticWidget({
    super.key,
    this.latitude,
    this.longitude,
    this.zoom = 14.0,
    this.myLocationEnabled = true,
    this.onMyLocationButtonPressed,
    this.onMapTap,
  });

  @override
  State<AmapStaticWidget> createState() => _AmapStaticWidgetState();
}

class _AmapStaticWidgetState extends State<AmapStaticWidget> {
  static const String _apiKey = 'e02aa654323adbcfdf59a3ae268f586d';
  
  @override
  Widget build(BuildContext context) {
    final lat = widget.latitude ?? 39.9042;
    final lng = widget.longitude ?? 116.4074;
    final zoom = widget.zoom.toInt();
    
    // 构建静态地图URL
    final staticMapUrl = _buildStaticMapUrl(lng, lat, zoom);
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // 静态地图图片
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                staticMapUrl,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '加载高德地图中...',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '地图加载失败',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '请检查网络连接',
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          
          // 地图信息覆盖层
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.map,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  const Text(
                    '高德地图',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 位置信息
          Positioned(
            bottom: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                '${lat.toStringAsFixed(4)}, ${lng.toStringAsFixed(4)}',
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          
          // 我的位置按钮
          if (widget.myLocationEnabled)
            Positioned(
              bottom: 16,
              right: 16,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Colors.white,
                foregroundColor: Colors.orange,
                onPressed: widget.onMyLocationButtonPressed,
                child: const Icon(Icons.my_location, size: 20),
              ),
            ),
          
          // 点击检测层
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                if (widget.onMapTap != null) {
                  widget.onMapTap!(lat, lng);
                }
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建静态地图URL
  String _buildStaticMapUrl(double lng, double lat, int zoom) {
    // 高德静态地图API参数
    final location = '$lng,$lat';
    const size = '750*400'; // 地图尺寸
    final markers = 'mid,,A:$lng,$lat'; // 标记点
    
    return 'https://restapi.amap.com/v3/staticmap?'
        'location=$location&'
        'zoom=$zoom&'
        'size=$size&'
        'markers=$markers&'
        'key=$_apiKey';
  }
}
