import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import '../../constants/app_colors.dart';
import '../../models/marker_model.dart';
import '../../services/mobile_features_service.dart';
import '../search_filter_chip.dart';

class AdvancedFilterDialog extends StatefulWidget {
  final String? selectedCategory;
  final List<String> selectedTags;
  final double? minRating;
  final bool? isPublic;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(AdvancedFilterOptions) onApply;

  const AdvancedFilterDialog({
    super.key,
    this.selectedCategory,
    this.selectedTags = const [],
    this.minRating,
    this.isPublic,
    this.startDate,
    this.endDate,
    required this.onApply,
  });

  @override
  State<AdvancedFilterDialog> createState() => _AdvancedFilterDialogState();
}

class _AdvancedFilterDialogState extends State<AdvancedFilterDialog> {
  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();
  
  String? _selectedCategory;
  List<String> _selectedTags = [];
  double? _minRating;
  bool? _isPublic;
  DateTime? _startDate;
  DateTime? _endDate;
  
  final List<String> _availableCategories = [
    '餐饮', '购物', '旅行', '运动', '娱乐', '工作', '学习', '医疗', '交通', '其他'
  ];
  
  final List<String> _popularTags = [
    '工作', '咖啡', '安静', '购物', '旅游', '摄影', '夜景', '健身', '运动', 
    '音乐', '演出', '美食', '聚会', '约会', '学习', '阅读', '放松'
  ];

  @override
  void initState() {
    super.initState();
    _initializeFilters();
    _mobileFeatures.initialize();
  }

  void _initializeFilters() {
    _selectedCategory = widget.selectedCategory;
    _selectedTags = List.from(widget.selectedTags);
    _minRating = widget.minRating;
    _isPublic = widget.isPublic;
    _startDate = widget.startDate;
    _endDate = widget.endDate;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCategoryFilter(),
                    const SizedBox(height: 24),
                    _buildTagsFilter(),
                    const SizedBox(height: 24),
                    _buildRatingFilter(),
                    const SizedBox(height: 24),
                    _buildPrivacyFilter(),
                    const SizedBox(height: 24),
                    _buildDateRangeFilter(),
                    const SizedBox(height: 24),
                    _buildActiveFilters(),
                  ],
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        children: [
          const HeroIcon(
            HeroIcons.adjustmentsHorizontal,
            style: HeroIconStyle.outline,
            size: 24,
            color: AppColors.primary,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              '高级筛选',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const HeroIcon(
              HeroIcons.xMark,
              style: HeroIconStyle.outline,
              size: 20,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey[100],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分类',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableCategories.map((category) {
            final isSelected = _selectedCategory == category;
            return GestureDetector(
              onTap: () async {
                await _mobileFeatures.combinedFeedback(
                  vibrationPattern: VibrationPattern.light,
                );
                setState(() {
                  _selectedCategory = isSelected ? null : category;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  category,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[700],
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTagsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标签',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _popularTags.map((tag) {
            final isSelected = _selectedTags.contains(tag);
            return GestureDetector(
              onTap: () async {
                await _mobileFeatures.combinedFeedback(
                  vibrationPattern: VibrationPattern.light,
                );
                setState(() {
                  if (isSelected) {
                    _selectedTags.remove(tag);
                  } else {
                    _selectedTags.add(tag);
                  }
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  '#$tag',
                  style: TextStyle(
                    color: isSelected ? AppColors.primary : Colors.grey[700],
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '最低评分',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: List.generate(5, (index) {
            final rating = index + 1;
            final isSelected = _minRating != null && _minRating! >= rating;
            return GestureDetector(
              onTap: () async {
                await _mobileFeatures.combinedFeedback(
                  vibrationPattern: VibrationPattern.light,
                );
                setState(() {
                  _minRating = _minRating == rating ? null : rating.toDouble();
                });
              },
              child: Container(
                margin: const EdgeInsets.only(right: 8),
                child: Icon(
                  Icons.star,
                  size: 32,
                  color: isSelected ? Colors.amber : Colors.grey[300],
                ),
              ),
            );
          }),
        ),
        if (_minRating != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              '${_minRating!.toInt()}星以上',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPrivacyFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '隐私设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildPrivacyOption('全部', null),
            const SizedBox(width: 12),
            _buildPrivacyOption('公开', true),
            const SizedBox(width: 12),
            _buildPrivacyOption('私密', false),
          ],
        ),
      ],
    );
  }

  Widget _buildPrivacyOption(String label, bool? value) {
    final isSelected = _isPublic == value;
    return GestureDetector(
      onTap: () async {
        await _mobileFeatures.combinedFeedback(
          vibrationPattern: VibrationPattern.light,
        );
        setState(() {
          _isPublic = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '创建时间',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDateButton(
                label: _startDate != null 
                  ? '${_startDate!.year}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}'
                  : '开始日期',
                onTap: () => _selectDate(true),
              ),
            ),
            const SizedBox(width: 12),
            const Text('至', style: TextStyle(color: Colors.grey)),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateButton(
                label: _endDate != null 
                  ? '${_endDate!.year}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}'
                  : '结束日期',
                onTap: () => _selectDate(false),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateButton({required String label, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 14,
                ),
              ),
            ),
            const HeroIcon(
              HeroIcons.calendar,
              style: HeroIconStyle.outline,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveFilters() {
    final hasFilters = _selectedCategory != null ||
        _selectedTags.isNotEmpty ||
        _minRating != null ||
        _isPublic != null ||
        _startDate != null ||
        _endDate != null;

    if (!hasFilters) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '当前筛选条件',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            if (_selectedCategory != null)
              SearchFilterChip(
                label: '分类: $_selectedCategory',
                onDeleted: () => setState(() => _selectedCategory = null),
              ),
            ..._selectedTags.map((tag) => SearchFilterChip(
              label: '标签: #$tag',
              onDeleted: () => setState(() => _selectedTags.remove(tag)),
            )),
            if (_minRating != null)
              SearchFilterChip(
                label: '评分: ${_minRating!.toInt()}星以上',
                onDeleted: () => setState(() => _minRating = null),
              ),
            if (_isPublic != null)
              SearchFilterChip(
                label: _isPublic! ? '公开' : '私密',
                onDeleted: () => setState(() => _isPublic = null),
              ),
            if (_startDate != null || _endDate != null)
              SearchFilterChip(
                label: '时间范围',
                onDeleted: () => setState(() {
                  _startDate = null;
                  _endDate = null;
                }),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _resetFilters,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('重置'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: FilledButton(
              onPressed: _applyFilters,
              style: FilledButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('应用筛选'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate 
        ? (_startDate ?? DateTime.now())
        : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.medium,
    );
    
    setState(() {
      _selectedCategory = null;
      _selectedTags.clear();
      _minRating = null;
      _isPublic = null;
      _startDate = null;
      _endDate = null;
    });
  }

  void _applyFilters() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.success,
    );

    final options = AdvancedFilterOptions(
      category: _selectedCategory,
      tags: _selectedTags,
      minRating: _minRating,
      isPublic: _isPublic,
      startDate: _startDate,
      endDate: _endDate,
    );

    widget.onApply(options);
    Navigator.of(context).pop();
  }
}

class AdvancedFilterOptions {
  final String? category;
  final List<String> tags;
  final double? minRating;
  final bool? isPublic;
  final DateTime? startDate;
  final DateTime? endDate;

  const AdvancedFilterOptions({
    this.category,
    this.tags = const [],
    this.minRating,
    this.isPublic,
    this.startDate,
    this.endDate,
  });

  bool get hasActiveFilters {
    return category != null ||
           tags.isNotEmpty ||
           minRating != null ||
           isPublic != null ||
           startDate != null ||
           endDate != null;
  }
}
