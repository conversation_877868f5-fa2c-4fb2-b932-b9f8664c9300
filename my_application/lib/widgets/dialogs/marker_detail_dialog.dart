import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import '../icon_library/icon_library.dart';
import '../../models/marker_model.dart';
import '../../constants/app_colors.dart';
import '../../services/share_service.dart';

/// 标记详情对话框
class MarkerDetailDialog extends StatefulWidget {
  final MarkerModel marker;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;

  const MarkerDetailDialog({
    super.key,
    required this.marker,
    this.onEdit,
    this.onDelete,
    this.onShare,
  });

  @override
  State<MarkerDetailDialog> createState() => _MarkerDetailDialogState();
}

class _MarkerDetailDialogState extends State<MarkerDetailDialog> {
  final PageController _imagePageController = PageController();
  int _currentImageIndex = 0;

  @override
  void dispose() {
    _imagePageController.dispose();
    super.dispose();
  }

  void _handleEdit() {
    if (widget.onEdit != null) {
      widget.onEdit!();
    }
    Navigator.of(context).pop();
  }

  void _handleDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这个收藏吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭确认对话框
              Navigator.of(context).pop(); // 关闭详情对话框
              if (widget.onDelete != null) {
                widget.onDelete!();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _handleShare() async {
    try {
      final shareService = ShareService();
      await shareService.shareMarker(widget.marker);
      
      if (widget.onShare != null) {
        widget.onShare!();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分享失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final iconItem = IconLibrary.defaultIcon;
    final categoryColor = IconCategories.getCategoryById(iconItem.category).color;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: categoryColor.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  IconDisplay(
                    iconItem: iconItem,
                    size: 24,
                    color: categoryColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.marker.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const HeroIcon(
                      HeroIcons.xMark,
                      style: HeroIconStyle.outline,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
            
            // 内容区域
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 基本信息
                    _buildBasicInfo(categoryColor),
                    
                    const SizedBox(height: 16),
                    
                    // 描述
                    if (widget.marker.description?.isNotEmpty ?? false) ...[
                      _buildDescription(),
                      const SizedBox(height: 16),
                    ],
                    
                    // 标签
                    if (widget.marker.tags.isNotEmpty) ...[
                      _buildTags(),
                      const SizedBox(height: 16),
                    ],
                    
                    // 图片
                    if (widget.marker.imagePaths.isNotEmpty) ...[
                      _buildImages(),
                      const SizedBox(height: 16),
                    ],
                    
                    // 位置信息
                    _buildLocationInfo(),
                    
                    const SizedBox(height: 16),
                    
                    // 时间信息
                    _buildTimeInfo(),
                  ],
                ),
              ),
            ),
            
            // 底部操作按钮
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo(Color categoryColor) {
    final category = IconCategories.getCategoryById(widget.marker.category);
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: categoryColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            category.name,
            style: TextStyle(
              fontSize: 12,
              color: categoryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const Spacer(),
        if (widget.marker.isPublic)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                HeroIcon(
                  HeroIcons.globeAlt,
                  style: HeroIconStyle.outline,
                  size: 12,
                  color: Colors.green[700],
                ),
                const SizedBox(width: 4),
                Text(
                  '公开',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.marker.description ?? '',
          style: const TextStyle(
            fontSize: 14,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildTags() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标签',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.marker.tags.map((tag) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '#$tag',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '图片',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (widget.marker.imagePaths.length > 1)
              Text(
                '${_currentImageIndex + 1}/${widget.marker.imagePaths.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _imagePageController,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
              });
            },
            itemCount: widget.marker.imagePaths.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    File(widget.marker.imagePaths[index]),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: const Center(
                          child: HeroIcon(
                            HeroIcons.photo,
                            style: HeroIconStyle.outline,
                            size: 48,
                            color: Colors.grey,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          HeroIcon(
            HeroIcons.mapPin,
            style: HeroIconStyle.solid,
            size: 16,
            color: Colors.blue[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '位置坐标',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
                Text(
                  '${widget.marker.latitude.toStringAsFixed(6)}, ${widget.marker.longitude.toStringAsFixed(6)}',
                  style: const TextStyle(fontSize: 11),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () {
              // TODO: 在地图上显示位置
            },
            child: const Text('查看'),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeInfo() {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const HeroIcon(
                HeroIcons.clock,
                style: HeroIconStyle.outline,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
              const Text(
                '创建时间',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                dateFormat.format(widget.marker.createdAt),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
          if (widget.marker.updatedAt != widget.marker.createdAt) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const HeroIcon(
                  HeroIcons.pencil,
                  style: HeroIconStyle.outline,
                  size: 16,
                  color: Colors.grey,
                ),
                const SizedBox(width: 8),
                const Text(
                  '更新时间',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  dateFormat.format(widget.marker.updatedAt ?? widget.marker.createdAt),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _handleShare,
              icon: const HeroIcon(
                HeroIcons.share,
                style: HeroIconStyle.outline,
                size: 16,
              ),
              label: const Text('分享'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _handleEdit,
              icon: const HeroIcon(
                HeroIcons.pencil,
                style: HeroIconStyle.outline,
                size: 16,
              ),
              label: const Text('编辑'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
          const SizedBox(width: 8),
          OutlinedButton(
            onPressed: _handleDelete,
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            ),
            child: const HeroIcon(
              HeroIcons.trash,
              style: HeroIconStyle.outline,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }
}
