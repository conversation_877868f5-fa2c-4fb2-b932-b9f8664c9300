import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../icon_library/icon_library.dart';
import '../../models/marker_model.dart';
import '../../constants/app_colors.dart';

/// 添加标记对话框
class AddMarkerDialog extends StatefulWidget {
  final double latitude;
  final double longitude;
  final Function(MarkerModel) onMarkerAdded;

  const AddMarkerDialog({
    super.key,
    required this.latitude,
    required this.longitude,
    required this.onMarkerAdded,
  });

  @override
  State<AddMarkerDialog> createState() => _AddMarkerDialogState();
}

class _AddMarkerDialogState extends State<AddMarkerDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  
  IconItem _selectedIcon = IconLibrary.defaultIcon;
  bool _isPublic = false;
  List<XFile> _selectedImages = [];
  List<String> _voiceNotes = [];
  bool _isRecording = false;
  String _selectedCategory = 'food';

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile> images = await picker.pickMultiImage();
    
    setState(() {
      _selectedImages.addAll(images);
      // 限制最多5张图片
      if (_selectedImages.length > 5) {
        _selectedImages = _selectedImages.take(5).toList();
      }
    });
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _handleSave() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final tags = _tagsController.text
        .split(' ')
        .where((tag) => tag.trim().isNotEmpty)
        .map((tag) => tag.trim())
        .toList();

    final marker = MarkerModel(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      latitude: widget.latitude,
      longitude: widget.longitude,
      category: _selectedCategory,
      iconId: _selectedIcon.id,
      tags: tags,
      isPublic: _isPublic,
      images: _selectedImages.map((image) => image.path).toList(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    widget.onMarkerAdded(marker);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // 标题栏
            Row(
              children: [
                const Text(
                  '添加收藏位置',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const HeroIcon(
                    HeroIcons.xMark,
                    style: HeroIconStyle.outline,
                    size: 20,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 位置信息
                      _buildLocationInfo(),
                      
                      const SizedBox(height: 20),
                      
                      // 图标选择
                      _buildIconSelector(),
                      
                      const SizedBox(height: 20),
                      
                      // 标题输入
                      _buildTitleField(),
                      
                      const SizedBox(height: 16),
                      
                      // 分类选择
                      _buildCategorySelector(),
                      
                      const SizedBox(height: 16),
                      
                      // 描述输入
                      _buildDescriptionField(),
                      
                      const SizedBox(height: 16),
                      
                      // 标签输入
                      _buildTagsField(),
                      
                      const SizedBox(height: 16),
                      
                      // 图片选择
                      _buildImageSelector(),
                      
                      const SizedBox(height: 16),
                      
                      // 公开设置
                      _buildPublicSwitch(),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 底部按钮
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          HeroIcon(
            HeroIcons.mapPin,
            style: HeroIconStyle.solid,
            size: 16,
            color: Colors.blue[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '位置坐标',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
                Text(
                  '${widget.latitude.toStringAsFixed(6)}, ${widget.longitude.toStringAsFixed(6)}',
                  style: const TextStyle(fontSize: 11),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIconSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择图标',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        CompactIconSelector(
          selectedIcon: _selectedIcon,
          onIconSelected: (icon) {
            setState(() {
              _selectedIcon = icon;
              _selectedCategory = icon.category;
            });
          },
          size: 48,
        ),
      ],
    );
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: '收藏名称 *',
        hintText: '请输入收藏位置的名称',
        border: OutlineInputBorder(),
        prefixIcon: HeroIcon(
          HeroIcons.bookmark,
          style: HeroIconStyle.outline,
          size: 20,
        ),
      ),
      maxLength: 50,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '请输入收藏名称';
        }
        return null;
      },
    );
  }

  Widget _buildCategorySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分类',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: IconCategories.categories.map((category) {
            final isSelected = _selectedCategory == category.id;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category.id;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? category.color.withValues(alpha: 0.2)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected 
                        ? category.color
                        : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  category.name,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? category.color : Colors.grey[600],
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: '描述 (可选)',
        hintText: '添加一些描述信息',
        border: OutlineInputBorder(),
        prefixIcon: HeroIcon(
          HeroIcons.documentText,
          style: HeroIconStyle.outline,
          size: 20,
        ),
      ),
      maxLines: 3,
      maxLength: 200,
    );
  }

  Widget _buildTagsField() {
    return TextFormField(
      controller: _tagsController,
      decoration: const InputDecoration(
        labelText: '标签 (可选)',
        hintText: '用空格分隔多个标签',
        border: OutlineInputBorder(),
        prefixIcon: HeroIcon(
          HeroIcons.hashtag,
          style: HeroIconStyle.outline,
          size: 20,
        ),
      ),
      maxLength: 100,
    );
  }

  Widget _buildImageSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '图片 (可选)',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            if (_selectedImages.length < 5)
              TextButton.icon(
                onPressed: _pickImages,
                icon: const HeroIcon(
                  HeroIcons.camera,
                  style: HeroIconStyle.outline,
                  size: 16,
                ),
                label: const Text('添加图片'),
              ),
          ],
        ),
        if (_selectedImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          File(_selectedImages[index].path),
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => _removeImage(index),
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const HeroIcon(
                              HeroIcons.xMark,
                              style: HeroIconStyle.outline,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPublicSwitch() {
    return Row(
      children: [
        const Text(
          '公开分享',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        Switch(
          value: _isPublic,
          onChanged: (value) {
            setState(() {
              _isPublic = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _handleSave,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('保存'),
          ),
        ),
      ],
    );
  }
}
