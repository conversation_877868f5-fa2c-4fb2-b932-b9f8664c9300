import 'package:flutter/material.dart';
import '../models/marker_model.dart';
import '../services/share_service.dart';
import '../constants/app_colors.dart';

class ShareDialog extends StatefulWidget {
  final MarkerModel? marker;
  final List<MarkerModel>? markers;

  const ShareDialog({
    super.key,
    this.marker,
    this.markers,
  }) : assert(marker != null || markers != null);

  @override
  State<ShareDialog> createState() => _ShareDialogState();
}

class _ShareDialogState extends State<ShareDialog> {
  final ShareService _shareService = ShareService();
  final TextEditingController _messageController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isMultiple = widget.markers != null;
    final title = isMultiple ? '分享 ${widget.markers!.length} 个地点' : '分享地点';

    return AlertDialog(
      title: Text(title),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 自定义消息输入
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: '自定义消息 (可选)',
                hintText: '添加一些个人说明...',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
              maxLength: 200,
            ),
            
            const SizedBox(height: 16),
            
            // 分享选项
            if (!isMultiple) ..._buildSingleMarkerOptions(),
            if (isMultiple) ..._buildMultipleMarkersOptions(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ],
    );
  }

  List<Widget> _buildSingleMarkerOptions() {
    return _shareService.getShareOptions().map((option) {
      return ListTile(
        leading: Icon(option.icon),
        title: Text(option.title),
        onTap: () => _handleShareAction(option.action),
        enabled: !_isLoading,
      );
    }).toList();
  }

  List<Widget> _buildMultipleMarkersOptions() {
    return [
      ListTile(
        leading: const Icon(Icons.text_fields),
        title: const Text('分享文本'),
        onTap: () => _handleShareAction(ShareAction.text),
        enabled: !_isLoading,
      ),
      ListTile(
        leading: const Icon(Icons.copy),
        title: const Text('复制到剪贴板'),
        onTap: () => _handleShareAction(ShareAction.clipboard),
        enabled: !_isLoading,
      ),
      ListTile(
        leading: const Icon(Icons.file_download),
        title: const Text('导出文件'),
        onTap: () => _handleShareAction(ShareAction.export),
        enabled: !_isLoading,
      ),
    ];
  }

  Future<void> _handleShareAction(ShareAction action) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final customMessage = _messageController.text.trim();
      
      switch (action) {
        case ShareAction.text:
          await _shareText(customMessage);
          break;
        case ShareAction.link:
          await _shareLink();
          break;
        case ShareAction.qrCode:
          await _showQRCode();
          break;
        case ShareAction.clipboard:
          await _copyToClipboard(customMessage);
          break;
        case ShareAction.export:
          await _exportAndShare();
          break;
      }
      
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('分享成功')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分享失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _shareText(String customMessage) async {
    if (widget.marker != null) {
      await _shareService.shareMarker(widget.marker!, customMessage: customMessage);
    } else {
      await _shareService.shareMultipleMarkers(widget.markers!, customMessage: customMessage);
    }
  }

  Future<void> _shareLink() async {
    if (widget.marker != null) {
      final link = await _shareService.generateShareLink(widget.marker!);
      await _shareService.copyToClipboard(link);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('分享链接已复制到剪贴板')),
        );
      }
    }
  }

  Future<void> _showQRCode() async {
    if (widget.marker != null) {
      final link = await _shareService.generateShareLink(widget.marker!);
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => _QRCodeDialog(content: link),
        );
      }
    }
  }

  Future<void> _copyToClipboard(String customMessage) async {
    // TODO: 需要访问私有方法，这里先用简单实现
    String text = customMessage.isNotEmpty ? customMessage : '分享内容';
    
    await _shareService.copyToClipboard(text);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('内容已复制到剪贴板')),
      );
    }
  }

  Future<void> _exportAndShare() async {
    final markers = widget.markers ?? [widget.marker!];
    await _shareService.exportAndShare(markers);
  }
}

class _QRCodeDialog extends StatelessWidget {
  final String content;

  const _QRCodeDialog({required this.content});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('二维码分享'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                '二维码\n(功能开发中)',
                textAlign: TextAlign.center,
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '扫描二维码查看分享内容',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('关闭'),
        ),
        FilledButton(
          onPressed: () async {
            await ShareService().copyToClipboard(content);
            if (context.mounted) {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('链接已复制到剪贴板')),
              );
            }
          },
          child: const Text('复制链接'),
        ),
      ],
    );
  }
}
