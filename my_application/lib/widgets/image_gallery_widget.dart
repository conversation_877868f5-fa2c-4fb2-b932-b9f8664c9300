import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'dart:io';

class ImageGalleryWidget extends StatelessWidget {
  final List<String> imagePaths;
  final String? heroTag;
  final int initialIndex;

  const ImageGalleryWidget({
    super.key,
    required this.imagePaths,
    this.heroTag,
    this.initialIndex = 0,
  });

  @override
  Widget build(BuildContext context) {
    if (imagePaths.isEmpty) {
      return Container(
        color: Colors.grey[300],
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            size: 64,
            color: Colors.grey,
          ),
        ),
      );
    }

    if (imagePaths.length == 1) {
      return GestureDetector(
        onTap: () => _openFullScreen(context, 0),
        child: Hero(
          tag: heroTag ?? imagePaths[0],
          child: _buildImage(imagePaths[0]),
        ),
      );
    }

    return PageView.builder(
      itemCount: imagePaths.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () => _openFullScreen(context, index),
          child: Hero(
            tag: '${heroTag ?? 'image'}_$index',
            child: _buildImage(imagePaths[index]),
          ),
        );
      },
    );
  }

  Widget _buildImage(String imagePath) {
    if (imagePath.startsWith('http')) {
      return Image.network(
        imagePath,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    } else {
      return Image.file(
        File(imagePath),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    }
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.broken_image,
          size: 64,
          color: Colors.grey,
        ),
      ),
    );
  }

  void _openFullScreen(BuildContext context, int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullScreenGallery(
          imagePaths: imagePaths,
          initialIndex: initialIndex,
          heroTag: heroTag,
        ),
      ),
    );
  }
}

class _FullScreenGallery extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;
  final String? heroTag;

  const _FullScreenGallery({
    required this.imagePaths,
    required this.initialIndex,
    this.heroTag,
  });

  @override
  State<_FullScreenGallery> createState() => _FullScreenGalleryState();
}

class _FullScreenGalleryState extends State<_FullScreenGallery> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        title: Text('${_currentIndex + 1} / ${widget.imagePaths.length}'),
        centerTitle: true,
      ),
      body: PhotoViewGallery.builder(
        pageController: _pageController,
        itemCount: widget.imagePaths.length,
        builder: (context, index) {
          final imagePath = widget.imagePaths[index];
          return PhotoViewGalleryPageOptions(
            imageProvider: imagePath.startsWith('http')
                ? NetworkImage(imagePath)
                : FileImage(File(imagePath)) as ImageProvider,
            heroAttributes: PhotoViewHeroAttributes(
              tag: '${widget.heroTag ?? 'image'}_$index',
            ),
            minScale: PhotoViewComputedScale.contained,
            maxScale: PhotoViewComputedScale.covered * 2,
          );
        },
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        scrollPhysics: const BouncingScrollPhysics(),
        backgroundDecoration: const BoxDecoration(
          color: Colors.black,
        ),
      ),
    );
  }
}
