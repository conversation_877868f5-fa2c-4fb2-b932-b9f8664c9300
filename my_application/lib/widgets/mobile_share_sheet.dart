import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../services/mobile_features_service.dart';
import '../services/share_service.dart';
import '../models/marker_model.dart';

/// 移动端分享面板
class MobileShareSheet extends StatefulWidget {
  final MarkerModel marker;
  final VoidCallback? onClose;

  const MobileShareSheet({
    super.key,
    required this.marker,
    this.onClose,
  });

  @override
  State<MobileShareSheet> createState() => _MobileShareSheetState();
}

class _MobileShareSheetState extends State<MobileShareSheet> {
  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();
  final ShareService _shareService = ShareService();
  bool _showQR = false;
  String? _shareUrl;

  @override
  void initState() {
    super.initState();
    _generateShareUrl();
  }

  Future<void> _generateShareUrl() async {
    final url = await _shareService.generateShareLink(widget.marker);
    if (mounted) {
      setState(() {
        _shareUrl = url;
      });
    }
  }

  Future<void> _handleSystemShare() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    if (_shareUrl != null) {
      final success = await _mobileFeatures.shareToSystem(
        title: widget.marker.title,
        text: widget.marker.description,
        url: _shareUrl!,
      );

      if (success && mounted) {
        widget.onClose?.call();
      }
    }
  }

  Future<void> _handleCopyLink() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    if (_shareUrl != null) {
      final success = await _mobileFeatures.copyToClipboard(_shareUrl!);
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('链接已复制到剪贴板'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _handleShowQR() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );
    
    setState(() {
      _showQR = true;
    });
  }

  Future<void> _handleEmailShare() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    if (_shareUrl != null) {
      await _mobileFeatures.openEmailApp(
        subject: widget.marker.title,
        body: '${widget.marker.description}\n\n查看详情: $_shareUrl',
      );
    }
  }

  Future<void> _handleSMSShare() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    if (_shareUrl != null) {
      await _mobileFeatures.openSMSApp(
        message: '${widget.marker.title}\n${widget.marker.description}\n\n查看详情: $_shareUrl',
      );
    }
  }

  Future<void> _handleMapShare() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.medium,
    );

    await _mobileFeatures.openMapApp(
      latitude: widget.marker.latitude,
      longitude: widget.marker.longitude,
      label: widget.marker.title,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_showQR) {
      return _buildQRView();
    }

    return _buildShareOptions();
  }

  Widget _buildQRView() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '分享二维码',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _showQR = false;
                  });
                },
                icon: const HeroIcon(
                  HeroIcons.xMark,
                  size: 20,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // 二维码
          if (_shareUrl != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: QrImageView(
                data: _shareUrl!,
                version: QrVersions.auto,
                size: 200,
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
              ),
            )
          else
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          
          const SizedBox(height: 16),
          
          Text(
            '扫描二维码访问收藏点',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 复制链接按钮
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _handleCopyLink,
              icon: const HeroIcon(
                HeroIcons.documentDuplicate,
                size: 16,
              ),
              label: const Text('复制链接'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareOptions() {
    final shareOptions = [
      if (_mobileFeatures.isInitialized) ...[
        _ShareOption(
          icon: HeroIcons.share,
          label: '系统分享',
          onTap: _handleSystemShare,
          available: true,
        ),
      ],
      _ShareOption(
        icon: HeroIcons.documentDuplicate,
        label: '复制链接',
        onTap: _handleCopyLink,
        available: true,
      ),
      _ShareOption(
        icon: HeroIcons.qrCode,
        label: '二维码',
        onTap: _handleShowQR,
        available: true,
      ),
      _ShareOption(
        icon: HeroIcons.envelope,
        label: '邮件',
        onTap: _handleEmailShare,
        available: true,
      ),
      _ShareOption(
        icon: HeroIcons.chatBubbleLeftRight,
        label: '短信',
        onTap: _handleSMSShare,
        available: true,
      ),
      _ShareOption(
        icon: HeroIcons.mapPin,
        label: '在地图中打开',
        onTap: _handleMapShare,
        available: true,
      ),
    ];

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '分享收藏点',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                onPressed: widget.onClose,
                icon: const HeroIcon(
                  HeroIcons.xMark,
                  size: 20,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 收藏点信息
          Text(
            widget.marker.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          if (widget.marker.description?.isNotEmpty ?? false) ...[
            const SizedBox(height: 4),
            Text(
              widget.marker.description ?? '',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          
          const SizedBox(height: 24),
          
          // 分享选项网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: shareOptions.where((option) => option.available).length,
            itemBuilder: (context, index) {
              final option = shareOptions.where((option) => option.available).elementAt(index);
              return _buildShareOptionItem(option);
            },
          ),
          
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildShareOptionItem(_ShareOption option) {
    return InkWell(
      onTap: option.onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            HeroIcon(
              option.icon,
              size: 24,
              color: Colors.grey.shade700,
            ),
            const SizedBox(height: 8),
            Text(
              option.label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _ShareOption {
  final HeroIcons icon;
  final String label;
  final VoidCallback onTap;
  final bool available;

  const _ShareOption({
    required this.icon,
    required this.label,
    required this.onTap,
    required this.available,
  });
}
