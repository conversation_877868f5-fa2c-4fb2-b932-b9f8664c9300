import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';

class TagsInputWidget extends StatefulWidget {
  final List<String> tags;
  final Function(List<String>) onTagsChanged;
  final int maxTags;

  const TagsInputWidget({
    super.key,
    required this.tags,
    required this.onTagsChanged,
    this.maxTags = AppConstants.maxTagsCount,
  });

  @override
  State<TagsInputWidget> createState() => _TagsInputWidgetState();
}

class _TagsInputWidgetState extends State<TagsInputWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '标签',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            Text(
              '${widget.tags.length}/${widget.maxTags}',
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // 标签输入框
        TextField(
          controller: _controller,
          focusNode: _focusNode,
          decoration: InputDecoration(
            hintText: '输入标签后按回车添加',
            prefixIcon: const Icon(Icons.tag),
            suffixIcon: _controller.text.isNotEmpty
                ? IconButton(
                    onPressed: _addTag,
                    icon: const Icon(Icons.add),
                  )
                : null,
          ),
          onSubmitted: (_) => _addTag(),
          onChanged: (value) {
            setState(() {}); // 更新UI以显示/隐藏添加按钮
          },
        ),
        
        const SizedBox(height: 8),
        
        // 标签列表
        if (widget.tags.isNotEmpty)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.tags.asMap().entries.map((entry) {
              final index = entry.key;
              final tag = entry.value;
              return _buildTagChip(tag, index);
            }).toList(),
          ),
        
        // 常用标签建议
        if (widget.tags.length < widget.maxTags) ...[
          const SizedBox(height: 12),
          const Text(
            '常用标签',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _getSuggestedTags().map((tag) {
              return _buildSuggestedTagChip(tag);
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildTagChip(String tag, int index) {
    return Chip(
      label: Text('#$tag'),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: () => _removeTag(index),
      backgroundColor: AppColors.primary.withOpacity(0.1),
      labelStyle: const TextStyle(
        color: AppColors.primary,
        fontWeight: FontWeight.w500,
      ),
      deleteIconColor: AppColors.primary,
      side: const BorderSide(color: AppColors.primary),
    );
  }

  Widget _buildSuggestedTagChip(String tag) {
    return ActionChip(
      label: Text('#$tag'),
      onPressed: () => _addSuggestedTag(tag),
      backgroundColor: Colors.grey[100],
      labelStyle: const TextStyle(
        color: AppColors.textSecondary,
        fontSize: 12,
      ),
      side: BorderSide(color: Colors.grey[300]!),
    );
  }

  void _addTag() {
    final tag = _controller.text.trim();
    if (tag.isNotEmpty && 
        !widget.tags.contains(tag) && 
        widget.tags.length < widget.maxTags) {
      final newTags = List<String>.from(widget.tags);
      newTags.add(tag);
      widget.onTagsChanged(newTags);
      _controller.clear();
    }
  }

  void _addSuggestedTag(String tag) {
    if (!widget.tags.contains(tag) && widget.tags.length < widget.maxTags) {
      final newTags = List<String>.from(widget.tags);
      newTags.add(tag);
      widget.onTagsChanged(newTags);
    }
  }

  void _removeTag(int index) {
    final newTags = List<String>.from(widget.tags);
    newTags.removeAt(index);
    widget.onTagsChanged(newTags);
  }

  List<String> _getSuggestedTags() {
    const allSuggestions = [
      '美食', '咖啡', '购物', '景点', '休闲', '娱乐',
      '运动', '学习', '工作', '聚会', '约会', '家庭',
      '推荐', '必去', '网红', '特色', '便宜', '高档',
    ];
    
    return allSuggestions
        .where((tag) => !widget.tags.contains(tag))
        .take(6)
        .toList();
  }
}
