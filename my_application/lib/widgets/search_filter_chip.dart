import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

class SearchFilterChip extends StatelessWidget {
  final String label;
  final VoidCallback onDeleted;

  const SearchFilterChip({
    super.key,
    required this.label,
    required this.onDeleted,
  });

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onDeleted,
      backgroundColor: AppColors.primary.withOpacity(0.1),
      labelStyle: const TextStyle(
        color: AppColors.primary,
        fontWeight: FontWeight.w500,
      ),
      deleteIconColor: AppColors.primary,
      side: const BorderSide(color: AppColors.primary),
    );
  }
}
