import 'package:flutter/material.dart';

class CategoryIconPicker extends StatefulWidget {
  final IconData? selectedIcon;
  final Function(IconData) onIconSelected;

  const CategoryIconPicker({
    super.key,
    this.selectedIcon,
    required this.onIconSelected,
  });

  @override
  State<CategoryIconPicker> createState() => _CategoryIconPickerState();
}

class _CategoryIconPickerState extends State<CategoryIconPicker> {
  static const List<IconData> _availableIcons = [
    Icons.restaurant,
    Icons.local_cafe,
    Icons.book,
    Icons.place,
    Icons.shopping_bag,
    Icons.work,
    Icons.flight,
    Icons.category,
    Icons.home,
    Icons.school,
    Icons.local_hospital,
    Icons.local_gas_station,
    Icons.local_parking,
    Icons.local_pharmacy,
    Icons.local_grocery_store,
    Icons.local_mall,
    Icons.local_movies,
    Icons.local_library,
    Icons.local_hotel,
    Icons.local_taxi,
    Icons.directions_bus,
    Icons.directions_subway,
    Icons.directions_train,
    Icons.directions_car,
    Icons.directions_bike,
    Icons.directions_walk,
    Icons.fitness_center,
    Icons.spa,
    Icons.beach_access,
    Icons.park,
    Icons.nature,
    Icons.pets,
    Icons.child_care,
    Icons.elderly,
    Icons.accessible,
    Icons.favorite,
    Icons.star,
    Icons.thumb_up,
    Icons.emoji_events,
    Icons.celebration,
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('选择图标'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _availableIcons.length,
          itemBuilder: (context, index) {
            final icon = _availableIcons[index];
            final isSelected = widget.selectedIcon == icon;
            
            return GestureDetector(
              onTap: () {
                widget.onIconSelected(icon);
                Navigator.of(context).pop();
              },
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected ? Colors.black : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Icon(
                  icon,
                  color: isSelected ? Colors.white : Colors.black,
                  size: 24,
                ),
              ),
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ],
    );
  }
}
