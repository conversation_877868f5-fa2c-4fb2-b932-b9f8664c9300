import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'icon_data.dart';

/// 图标显示组件
class IconDisplay extends StatelessWidget {
  final IconItem iconItem;
  final double size;
  final Color? color;
  final Color? backgroundColor;
  final bool showBackground;
  final double borderRadius;
  final VoidCallback? onTap;

  const IconDisplay({
    super.key,
    required this.iconItem,
    this.size = 24,
    this.color,
    this.backgroundColor,
    this.showBackground = false,
    this.borderRadius = 8,
    this.onTap,
  });

  /// 根据图标ID创建显示组件
  factory IconDisplay.fromId({
    required String iconId,
    double size = 24,
    Color? color,
    Color? backgroundColor,
    bool showBackground = false,
    double borderRadius = 8,
    VoidCallback? onTap,
  }) {
    final iconItem = IconLibrary.getIconById(iconId) ?? IconLibrary.defaultIcon;
    return IconDisplay(
      iconItem: iconItem,
      size: size,
      color: color,
      backgroundColor: backgroundColor,
      showBackground: showBackground,
      borderRadius: borderRadius,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Theme.of(context).iconTheme.color;
    final bgColor = backgroundColor ?? IconCategories.getCategoryById(iconItem.category).color;

    Widget iconWidget = HeroIcon(
      iconItem.icon,
      style: HeroIconStyle.outline,
      size: size,
      color: iconColor,
    );

    if (showBackground) {
      iconWidget = Container(
        width: size + 16,
        height: size + 16,
        decoration: BoxDecoration(
          color: bgColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: bgColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Center(child: iconWidget),
      );
    }

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}

/// 图标选择器网格项
class IconGridItem extends StatelessWidget {
  final IconItem iconItem;
  final bool isSelected;
  final VoidCallback onTap;
  final double size;

  const IconGridItem({
    super.key,
    required this.iconItem,
    required this.isSelected,
    required this.onTap,
    this.size = 48,
  });

  @override
  Widget build(BuildContext context) {
    final categoryColor = IconCategories.getCategoryById(iconItem.category).color;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected 
              ? categoryColor.withValues(alpha: 0.2)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? categoryColor
                : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            HeroIcon(
              iconItem.icon,
              style: HeroIconStyle.outline,
              size: size * 0.5,
              color: isSelected ? categoryColor : Colors.grey[600],
            ),
            const SizedBox(height: 4),
            Text(
              iconItem.name,
              style: TextStyle(
                fontSize: 10,
                color: isSelected ? categoryColor : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

/// 分类标签
class CategoryChip extends StatelessWidget {
  final IconCategory category;
  final bool isSelected;
  final VoidCallback onTap;

  const CategoryChip({
    super.key,
    required this.category,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? category.color.withValues(alpha: 0.2)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected 
                ? category.color
                : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeroIcon(
              category.icon,
              style: HeroIconStyle.outline,
              size: 16,
              color: isSelected ? category.color : Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              category.name,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? category.color : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 图标预览卡片
class IconPreviewCard extends StatelessWidget {
  final IconItem? selectedIcon;
  final VoidCallback onTap;

  const IconPreviewCard({
    super.key,
    this.selectedIcon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final icon = selectedIcon ?? IconLibrary.defaultIcon;
    final categoryColor = IconCategories.getCategoryById(icon.category).color;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: categoryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: categoryColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: HeroIcon(
                  icon.icon,
                  style: HeroIconStyle.outline,
                  size: 24,
                  color: categoryColor,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    icon.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    IconCategories.getCategoryById(icon.category).name,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            HeroIcon(
              HeroIcons.chevronRight,
              style: HeroIconStyle.outline,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
