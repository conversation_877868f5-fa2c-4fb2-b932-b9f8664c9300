import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'icon_data.dart';
import 'icon_display.dart';

/// 图标选择器对话框
class IconSelectorDialog extends StatefulWidget {
  final IconItem? initialIcon;
  final Function(IconItem) onIconSelected;

  const IconSelectorDialog({
    super.key,
    this.initialIcon,
    required this.onIconSelected,
  });

  @override
  State<IconSelectorDialog> createState() => _IconSelectorDialogState();
}

class _IconSelectorDialogState extends State<IconSelectorDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategoryId = 'food';
  IconItem? _selectedIcon;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedIcon = widget.initialIcon;
    if (_selectedIcon != null) {
      _selectedCategoryId = _selectedIcon!.category;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<IconItem> get _filteredIcons {
    List<IconItem> icons;
    
    if (_searchQuery.isNotEmpty) {
      icons = IconLibrary.searchIcons(_searchQuery);
    } else {
      icons = IconLibrary.getIconsByCategory(_selectedCategoryId);
    }
    
    return icons;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 标题栏
            Row(
              children: [
                const Text(
                  '选择图标',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const HeroIcon(
                    HeroIcons.xMark,
                    style: HeroIconStyle.outline,
                    size: 20,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 搜索栏
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索图标...',
                prefixIcon: const HeroIcon(
                  HeroIcons.magnifyingGlass,
                  style: HeroIconStyle.outline,
                  size: 20,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                        icon: const HeroIcon(
                          HeroIcons.xMark,
                          style: HeroIconStyle.outline,
                          size: 16,
                        ),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.blue[600]!),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // 分类标签（仅在非搜索状态下显示）
            if (_searchQuery.isEmpty) ...[
              SizedBox(
                height: 40,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: IconCategories.categories.length,
                  itemBuilder: (context, index) {
                    final category = IconCategories.categories[index];
                    return CategoryChip(
                      category: category,
                      isSelected: _selectedCategoryId == category.id,
                      onTap: () {
                        setState(() {
                          _selectedCategoryId = category.id;
                        });
                      },
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 图标网格
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1,
                ),
                itemCount: _filteredIcons.length,
                itemBuilder: (context, index) {
                  final icon = _filteredIcons[index];
                  return IconGridItem(
                    iconItem: icon,
                    isSelected: _selectedIcon?.id == icon.id,
                    onTap: () {
                      setState(() {
                        _selectedIcon = icon;
                      });
                    },
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 底部按钮
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _selectedIcon != null
                        ? () {
                            widget.onIconSelected(_selectedIcon!);
                            Navigator.of(context).pop();
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('确定'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 图标选择器按钮
class IconSelectorButton extends StatelessWidget {
  final IconItem? selectedIcon;
  final Function(IconItem) onIconSelected;
  final String? label;

  const IconSelectorButton({
    super.key,
    this.selectedIcon,
    required this.onIconSelected,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        IconPreviewCard(
          selectedIcon: selectedIcon,
          onTap: () {
            showDialog(
              context: context,
              builder: (context) => IconSelectorDialog(
                initialIcon: selectedIcon,
                onIconSelected: onIconSelected,
              ),
            );
          },
        ),
      ],
    );
  }
}

/// 简化的图标选择器（用于表单中）
class CompactIconSelector extends StatelessWidget {
  final IconItem? selectedIcon;
  final Function(IconItem) onIconSelected;
  final double size;

  const CompactIconSelector({
    super.key,
    this.selectedIcon,
    required this.onIconSelected,
    this.size = 40,
  });

  @override
  Widget build(BuildContext context) {
    final icon = selectedIcon ?? IconLibrary.defaultIcon;
    final categoryColor = IconCategories.getCategoryById(icon.category).color;

    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) => IconSelectorDialog(
            initialIcon: selectedIcon,
            onIconSelected: onIconSelected,
          ),
        );
      },
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: categoryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: categoryColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Center(
          child: HeroIcon(
            icon.icon,
            style: HeroIconStyle.outline,
            size: size * 0.6,
            color: categoryColor,
          ),
        ),
      ),
    );
  }
}
