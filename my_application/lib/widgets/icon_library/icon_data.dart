import 'package:heroicons/heroicons.dart';
import 'package:flutter/material.dart';

/// 图标项目数据模型
class IconItem {
  final String id;
  final String name;
  final String category;
  final HeroIcons icon;
  final List<String> tags;

  const IconItem({
    required this.id,
    required this.name,
    required this.category,
    required this.icon,
    this.tags = const [],
  });
}

/// 图标分类
class IconCategory {
  final String id;
  final String name;
  final HeroIcons icon;
  final Color color;

  const IconCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}

/// 预定义的图标分类
class IconCategories {
  static const List<IconCategory> categories = [
    IconCategory(
      id: 'food',
      name: '餐饮',
      icon: HeroIcons.buildingStorefront,
      color: Colors.orange,
    ),
    IconCategory(
      id: 'coffee',
      name: '咖啡',
      icon: HeroIcons.beaker,
      color: Colors.brown,
    ),
    IconCategory(
      id: 'shopping',
      name: '购物',
      icon: HeroIcons.shoppingBag,
      color: Colors.pink,
    ),
    IconCategory(
      id: 'travel',
      name: '旅行',
      icon: HeroIcons.mapPin,
      color: Colors.blue,
    ),
    IconCategory(
      id: 'entertainment',
      name: '娱乐',
      icon: HeroIcons.film,
      color: Colors.purple,
    ),
    IconCategory(
      id: 'sports',
      name: '运动',
      icon: HeroIcons.trophy,
      color: Colors.green,
    ),
    IconCategory(
      id: 'work',
      name: '工作',
      icon: HeroIcons.briefcase,
      color: Colors.indigo,
    ),
    IconCategory(
      id: 'health',
      name: '健康',
      icon: HeroIcons.heart,
      color: Colors.red,
    ),
    IconCategory(
      id: 'education',
      name: '教育',
      icon: HeroIcons.academicCap,
      color: Colors.teal,
    ),
    IconCategory(
      id: 'transport',
      name: '交通',
      icon: HeroIcons.truck,
      color: Colors.grey,
    ),
    IconCategory(
      id: 'service',
      name: '服务',
      icon: HeroIcons.wrench,
      color: Colors.amber,
    ),
    IconCategory(
      id: 'other',
      name: '其他',
      icon: HeroIcons.ellipsisHorizontal,
      color: Colors.blueGrey,
    ),
  ];

  static IconCategory getCategoryById(String id) {
    return categories.firstWhere(
      (category) => category.id == id,
      orElse: () => categories.last, // 默认返回"其他"分类
    );
  }
}

/// 预定义的图标库
class IconLibrary {
  static const List<IconItem> icons = [
    // 餐饮类
    IconItem(
      id: 'restaurant',
      name: '餐厅',
      category: 'food',
      icon: HeroIcons.buildingStorefront,
      tags: ['餐厅', '美食', '用餐'],
    ),
    IconItem(
      id: 'utensils',
      name: '餐具',
      category: 'food',
      icon: HeroIcons.scissors,
      tags: ['餐具', '刀叉', '用餐'],
    ),
    IconItem(
      id: 'pizza',
      name: '披萨',
      category: 'food',
      icon: HeroIcons.cake,
      tags: ['披萨', '快餐', '意式'],
    ),
    
    // 咖啡类
    IconItem(
      id: 'coffee',
      name: '咖啡',
      category: 'coffee',
      icon: HeroIcons.beaker,
      tags: ['咖啡', '饮品', '咖啡厅'],
    ),
    IconItem(
      id: 'cafe',
      name: '咖啡厅',
      category: 'coffee',
      icon: HeroIcons.buildingOffice,
      tags: ['咖啡厅', '休闲', '聚会'],
    ),
    
    // 购物类
    IconItem(
      id: 'shopping_bag',
      name: '购物袋',
      category: 'shopping',
      icon: HeroIcons.shoppingBag,
      tags: ['购物', '商店', '消费'],
    ),
    IconItem(
      id: 'shopping_cart',
      name: '购物车',
      category: 'shopping',
      icon: HeroIcons.shoppingCart,
      tags: ['购物车', '超市', '采购'],
    ),
    IconItem(
      id: 'store',
      name: '商店',
      category: 'shopping',
      icon: HeroIcons.buildingStorefront,
      tags: ['商店', '零售', '购买'],
    ),
    
    // 旅行类
    IconItem(
      id: 'map_pin',
      name: '地点',
      category: 'travel',
      icon: HeroIcons.mapPin,
      tags: ['地点', '位置', '标记'],
    ),
    IconItem(
      id: 'airplane',
      name: '飞机',
      category: 'travel',
      icon: HeroIcons.paperAirplane,
      tags: ['飞机', '旅行', '出行'],
    ),
    IconItem(
      id: 'camera',
      name: '相机',
      category: 'travel',
      icon: HeroIcons.camera,
      tags: ['相机', '拍照', '记录'],
    ),
    
    // 娱乐类
    IconItem(
      id: 'film',
      name: '电影',
      category: 'entertainment',
      icon: HeroIcons.film,
      tags: ['电影', '影院', '娱乐'],
    ),
    IconItem(
      id: 'music',
      name: '音乐',
      category: 'entertainment',
      icon: HeroIcons.musicalNote,
      tags: ['音乐', '演出', '娱乐'],
    ),
    IconItem(
      id: 'game',
      name: '游戏',
      category: 'entertainment',
      icon: HeroIcons.puzzlePiece,
      tags: ['游戏', '娱乐', '休闲'],
    ),
    
    // 运动类
    IconItem(
      id: 'trophy',
      name: '奖杯',
      category: 'sports',
      icon: HeroIcons.trophy,
      tags: ['奖杯', '胜利', '成就'],
    ),
    IconItem(
      id: 'fitness',
      name: '健身',
      category: 'sports',
      icon: HeroIcons.fire,
      tags: ['健身', '运动', '锻炼'],
    ),
    
    // 工作类
    IconItem(
      id: 'briefcase',
      name: '公文包',
      category: 'work',
      icon: HeroIcons.briefcase,
      tags: ['工作', '办公', '商务'],
    ),
    IconItem(
      id: 'office',
      name: '办公室',
      category: 'work',
      icon: HeroIcons.buildingOffice,
      tags: ['办公室', '工作', '公司'],
    ),
    
    // 健康类
    IconItem(
      id: 'heart',
      name: '心形',
      category: 'health',
      icon: HeroIcons.heart,
      tags: ['心形', '喜爱', '健康'],
    ),
    IconItem(
      id: 'medical',
      name: '医疗',
      category: 'health',
      icon: HeroIcons.plus,
      tags: ['医疗', '健康', '医院'],
    ),
    
    // 教育类
    IconItem(
      id: 'book',
      name: '书籍',
      category: 'education',
      icon: HeroIcons.bookOpen,
      tags: ['书籍', '阅读', '学习'],
    ),
    IconItem(
      id: 'graduation',
      name: '毕业帽',
      category: 'education',
      icon: HeroIcons.academicCap,
      tags: ['毕业', '教育', '学校'],
    ),
    
    // 交通类
    IconItem(
      id: 'car',
      name: '汽车',
      category: 'transport',
      icon: HeroIcons.truck,
      tags: ['汽车', '交通', '出行'],
    ),
    
    // 服务类
    IconItem(
      id: 'wrench',
      name: '扳手',
      category: 'service',
      icon: HeroIcons.wrench,
      tags: ['维修', '服务', '工具'],
    ),
    IconItem(
      id: 'home',
      name: '家',
      category: 'service',
      icon: HeroIcons.home,
      tags: ['家', '住宅', '居住'],
    ),
    
    // 其他类
    IconItem(
      id: 'star',
      name: '星星',
      category: 'other',
      icon: HeroIcons.star,
      tags: ['星星', '收藏', '特别'],
    ),
    IconItem(
      id: 'bookmark',
      name: '书签',
      category: 'other',
      icon: HeroIcons.bookmark,
      tags: ['书签', '收藏', '标记'],
    ),
  ];

  /// 根据分类获取图标
  static List<IconItem> getIconsByCategory(String categoryId) {
    return icons.where((icon) => icon.category == categoryId).toList();
  }

  /// 根据ID获取图标
  static IconItem? getIconById(String id) {
    try {
      return icons.firstWhere((icon) => icon.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 搜索图标
  static List<IconItem> searchIcons(String query) {
    if (query.isEmpty) return icons;
    
    final lowerQuery = query.toLowerCase();
    return icons.where((icon) {
      return icon.name.toLowerCase().contains(lowerQuery) ||
             icon.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// 获取默认图标
  static IconItem get defaultIcon => icons.first;
}
