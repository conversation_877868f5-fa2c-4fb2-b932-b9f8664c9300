import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:convert';

/// 高德地图WebView组件
class AmapWebViewWidget extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  final double zoom;
  final bool myLocationEnabled;
  final VoidCallback? onMyLocationButtonPressed;
  final Function(double lat, double lng)? onMapTap;

  const AmapWebViewWidget({
    super.key,
    this.latitude,
    this.longitude,
    this.zoom = 14.0,
    this.myLocationEnabled = true,
    this.onMyLocationButtonPressed,
    this.onMapTap,
  });

  @override
  State<AmapWebViewWidget> createState() => _AmapWebViewWidgetState();
}

class _AmapWebViewWidgetState extends State<AmapWebViewWidget> {
  late final WebViewController _controller;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _errorMessage = null;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _initializeMap();
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _isLoading = false;
              _errorMessage = '地图加载失败: ${error.description}';
            });
          },
        ),
      )
      ..addJavaScriptChannel(
        'AmapFlutter',
        onMessageReceived: (JavaScriptMessage message) {
          _handleJavaScriptMessage(message.message);
        },
      )
      ..loadHtmlString(_getAmapHtml());
  }

  void _handleJavaScriptMessage(String message) {
    try {
      final data = json.decode(message);
      final type = data['type'];
      
      switch (type) {
        case 'mapTap':
          if (widget.onMapTap != null) {
            widget.onMapTap!(data['lat'], data['lng']);
          }
          break;
        case 'mapReady':
          print('高德地图WebView已准备就绪');
          break;
        case 'error':
          setState(() {
            _errorMessage = data['message'];
          });
          break;
      }
    } catch (e) {
      print('处理JavaScript消息失败: $e');
    }
  }

  void _initializeMap() {
    final lat = widget.latitude ?? 39.9042;
    final lng = widget.longitude ?? 116.4074;
    final zoom = widget.zoom;

    _controller.runJavaScript('''
      if (typeof initMap === 'function') {
        initMap($lat, $lng, $zoom);
      }
    ''');
  }

  String _getAmapHtml() {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>高德地图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        #mapContainer {
            width: 100%;
            height: 100vh;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: Arial, sans-serif;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="mapContainer">
        <div class="loading">正在加载高德地图...</div>
    </div>
    
    <!-- 高德地图JavaScript API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=e02aa654323adbcfdf59a3ae268f586d"></script>
    
    <script>
        let map = null;
        let marker = null;
        
        // 初始化地图
        function initMap(lat, lng, zoom) {
            try {
                // 创建地图实例
                map = new AMap.Map('mapContainer', {
                    center: [lng, lat], // 高德地图使用 [经度, 纬度] 格式
                    zoom: zoom,
                    mapStyle: 'amap://styles/normal', // 标准样式
                    viewMode: '2D', // 2D视图
                    lang: 'zh_cn' // 中文
                });
                
                // 添加地图控件
                map.addControl(new AMap.Scale());
                map.addControl(new AMap.ToolBar());
                
                // 添加标记
                addMarker(lat, lng);
                
                // 地图点击事件
                map.on('click', function(e) {
                    const lnglat = e.lnglat;
                    sendMessage({
                        type: 'mapTap',
                        lat: lnglat.lat,
                        lng: lnglat.lng
                    });
                });
                
                // 地图加载完成
                map.on('complete', function() {
                    sendMessage({
                        type: 'mapReady'
                    });
                });
                
                console.log('高德地图初始化成功');
                
            } catch (error) {
                console.error('地图初始化失败:', error);
                sendMessage({
                    type: 'error',
                    message: '地图初始化失败: ' + error.message
                });
            }
        }
        
        // 添加标记
        function addMarker(lat, lng) {
            if (marker) {
                map.remove(marker);
            }
            
            marker = new AMap.Marker({
                position: [lng, lat],
                title: '当前位置',
                icon: new AMap.Icon({
                    size: new AMap.Size(25, 34),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
                })
            });
            
            map.add(marker);
        }
        
        // 更新地图中心和标记
        function updateLocation(lat, lng) {
            if (map) {
                map.setCenter([lng, lat]);
                addMarker(lat, lng);
            }
        }
        
        // 发送消息到Flutter
        function sendMessage(data) {
            if (window.AmapFlutter && window.AmapFlutter.postMessage) {
                window.AmapFlutter.postMessage(JSON.stringify(data));
            }
        }
        
        // 错误处理
        window.onerror = function(msg, url, line, col, error) {
            sendMessage({
                type: 'error',
                message: 'JavaScript错误: ' + msg
            });
        };
    </script>
</body>
</html>
    ''';
  }

  /// 更新地图位置
  void updateLocation(double latitude, double longitude) {
    _controller.runJavaScript('''
      if (typeof updateLocation === 'function') {
        updateLocation($latitude, $longitude);
      }
    ''');
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        WebViewWidget(controller: _controller),
        
        // 加载指示器
        if (_isLoading)
          Container(
            color: Colors.white,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    '正在加载高德地图...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        
        // 错误信息
        if (_errorMessage != null)
          Container(
            color: Colors.white,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                        _isLoading = true;
                      });
                      _controller.reload();
                    },
                    child: const Text('重新加载'),
                  ),
                ],
              ),
            ),
          ),
        
        // 我的位置按钮
        if (widget.myLocationEnabled && widget.onMyLocationButtonPressed != null)
          Positioned(
            right: 16,
            bottom: 16,
            child: FloatingActionButton(
              mini: true,
              onPressed: widget.onMyLocationButtonPressed,
              backgroundColor: Colors.white,
              foregroundColor: Colors.blue,
              child: const Icon(Icons.my_location),
            ),
          ),
      ],
    );
  }
}
