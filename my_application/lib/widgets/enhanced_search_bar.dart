import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import '../constants/app_colors.dart';
import '../services/mobile_features_service.dart';

class EnhancedSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String hintText;
  final bool hasActiveFilters;
  final VoidCallback onFilterTap;
  final ValueChanged<String> onChanged;
  final ValueChanged<String> onSubmitted;
  final VoidCallback? onClear;

  const EnhancedSearchBar({
    super.key,
    required this.controller,
    required this.focusNode,
    this.hintText = '搜索收藏...',
    this.hasActiveFilters = false,
    required this.onFilterTap,
    required this.onChanged,
    required this.onSubmitted,
    this.onClear,
  });

  @override
  State<EnhancedSearchBar> createState() => _EnhancedSearchBarState();
}

class _EnhancedSearchBarState extends State<EnhancedSearchBar>
    with SingleTickerProviderStateMixin {
  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _mobileFeatures.initialize();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: Colors.grey[100],
      end: AppColors.primary.withOpacity(0.1),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.controller,
              focusNode: widget.focusNode,
              onChanged: widget.onChanged,
              onSubmitted: widget.onSubmitted,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 16,
                ),
                prefixIcon: const Padding(
                  padding: EdgeInsets.all(12),
                  child: HeroIcon(
                    HeroIcons.magnifyingGlass,
                    style: HeroIconStyle.outline,
                    size: 20,
                    color: Colors.grey,
                  ),
                ),
                suffixIcon: widget.controller.text.isNotEmpty
                    ? IconButton(
                        onPressed: _handleClear,
                        icon: const HeroIcon(
                          HeroIcons.xMark,
                          style: HeroIconStyle.outline,
                          size: 18,
                          color: Colors.grey,
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
            ),
          ),
          _buildFilterButton(),
        ],
      ),
    );
  }

  Widget _buildFilterButton() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: widget.hasActiveFilters 
                  ? AppColors.primary.withOpacity(0.1)
                  : _colorAnimation.value,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.hasActiveFilters 
                    ? AppColors.primary
                    : Colors.grey[300]!,
                width: widget.hasActiveFilters ? 1.5 : 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _handleFilterTap,
                onTapDown: (_) => _animationController.forward(),
                onTapUp: (_) => _animationController.reverse(),
                onTapCancel: () => _animationController.reverse(),
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Stack(
                    children: [
                      HeroIcon(
                        HeroIcons.adjustmentsHorizontal,
                        style: HeroIconStyle.outline,
                        size: 20,
                        color: widget.hasActiveFilters 
                            ? AppColors.primary
                            : Colors.grey[600],
                      ),
                      if (widget.hasActiveFilters)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleClear() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );
    
    widget.controller.clear();
    widget.onChanged('');
    widget.onClear?.call();
  }

  void _handleFilterTap() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );
    
    widget.onFilterTap();
  }
}

class SearchSuggestionsList extends StatelessWidget {
  final List<String> suggestions;
  final List<String> history;
  final ValueChanged<String> onSuggestionTap;
  final ValueChanged<String> onHistoryDelete;
  final VoidCallback? onHistoryClear;

  const SearchSuggestionsList({
    super.key,
    required this.suggestions,
    required this.history,
    required this.onSuggestionTap,
    required this.onHistoryDelete,
    this.onHistoryClear,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (history.isNotEmpty) ...[
            _buildSectionHeader(
              '搜索历史',
              onClear: onHistoryClear,
            ),
            ...history.map((item) => _buildSuggestionItem(
              item,
              HeroIcons.clock,
              onTap: () => onSuggestionTap(item),
              onDelete: () => onHistoryDelete(item),
            )),
          ],
          if (suggestions.isNotEmpty) ...[
            _buildSectionHeader('搜索建议'),
            ...suggestions.map((item) => _buildSuggestionItem(
              item,
              HeroIcons.magnifyingGlass,
              onTap: () => onSuggestionTap(item),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, {VoidCallback? onClear}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          const Spacer(),
          if (onClear != null)
            GestureDetector(
              onTap: onClear,
              child: Text(
                '清空',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(
    String text,
    HeroIcons icon, {
    required VoidCallback onTap,
    VoidCallback? onDelete,
  }) {
    return ListTile(
      leading: HeroIcon(
        icon,
        style: HeroIconStyle.outline,
        size: 18,
        color: Colors.grey[600],
      ),
      title: Text(
        text,
        style: const TextStyle(fontSize: 14),
      ),
      trailing: onDelete != null
          ? IconButton(
              onPressed: onDelete,
              icon: HeroIcon(
                HeroIcons.xMark,
                style: HeroIconStyle.outline,
                size: 16,
                color: Colors.grey[400],
              ),
            )
          : null,
      onTap: onTap,
      dense: true,
    );
  }
}
