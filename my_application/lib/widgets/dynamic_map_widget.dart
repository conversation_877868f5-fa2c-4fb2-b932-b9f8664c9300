import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import '../services/map_service_manager.dart';
import 'amap_web_widget.dart';

/// 动态地图组件
/// 根据地图服务类型自动渲染Google Maps或高德地图
class DynamicMapWidget extends StatefulWidget {
  final Function(dynamic)? onMapCreated;
  final Set<gmaps.Marker>? googleMarkers;
  final gmaps.CameraPosition? initialCameraPosition;
  final bool myLocationEnabled;
  final VoidCallback? onMyLocationButtonPressed;
  final Function(double lat, double lng)? onMapTap;
  final Function(double lat, double lng)? onMapLongPress;

  const DynamicMapWidget({
    super.key,
    this.onMapCreated,
    this.googleMarkers,
    this.initialCameraPosition,
    this.myLocationEnabled = true,
    this.onMyLocationButtonPressed,
    this.onMapTap,
    this.onMapLongPress,
  });

  @override
  State<DynamicMapWidget> createState() => _DynamicMapWidgetState();
}

class _DynamicMapWidgetState extends State<DynamicMapWidget> {
  final MapServiceManager _mapServiceManager = MapServiceManager();
  MapServiceType? _currentMapService;
  bool _isLoading = true;
  String _loadingMessage = '正在初始化地图服务...';

  @override
  void initState() {
    super.initState();
    _initializeMapService();
  }

  /// 初始化地图服务
  Future<void> _initializeMapService() async {
    try {
      setState(() {
        _loadingMessage = '正在检测您的位置...';
      });

      final mapService = await _mapServiceManager.initializeMapService();
      
      setState(() {
        _currentMapService = mapService;
        _isLoading = false;
        _loadingMessage = '地图加载完成';
      });

      print('动态地图组件：地图服务初始化完成 - ${_mapServiceManager.getMapServiceDisplayName(mapService)}');
      
    } catch (e) {
      print('动态地图组件：初始化失败 - $e');
      setState(() {
        _currentMapService = MapServiceType.googleMaps; // 默认使用Google Maps
        _isLoading = false;
        _loadingMessage = '使用默认地图服务';
      });
    }
  }

  /// 构建Google Maps
  Widget _buildGoogleMap() {
    return gmaps.GoogleMap(
      onMapCreated: (gmaps.GoogleMapController controller) {
        if (widget.onMapCreated != null) {
          widget.onMapCreated!(controller);
        }
      },
      initialCameraPosition: widget.initialCameraPosition ??
        const gmaps.CameraPosition(
          target: gmaps.LatLng(39.9042, 116.4074), // 北京天安门
          zoom: 14.0,
        ),
      markers: widget.googleMarkers ?? {},
      myLocationEnabled: widget.myLocationEnabled,
      myLocationButtonEnabled: false, // 我们使用自定义按钮
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      compassEnabled: true,
      rotateGesturesEnabled: true,
      scrollGesturesEnabled: true,
      tiltGesturesEnabled: true,
      zoomGesturesEnabled: true,
    );
  }

  /// 构建高德地图
  Widget _buildAmapMap() {
    print('动态地图组件：开始构建高德地图');

    // 优先使用地图服务管理器获取的真实位置
    double latitude = 39.9042; // 默认位置（北京）
    double longitude = 116.4074;
    double zoom = 14.0;

    if (_mapServiceManager.userPosition != null) {
      latitude = _mapServiceManager.userPosition!.latitude;
      longitude = _mapServiceManager.userPosition!.longitude;
      zoom = 16.0; // 使用更高的缩放级别显示用户位置
      print('动态地图组件：使用真实位置 - 纬度: $latitude, 经度: $longitude');
    } else if (widget.initialCameraPosition != null) {
      latitude = widget.initialCameraPosition!.target.latitude;
      longitude = widget.initialCameraPosition!.target.longitude;
      zoom = widget.initialCameraPosition!.zoom;
      print('动态地图组件：使用传入位置 - 纬度: $latitude, 经度: $longitude');
    } else {
      print('动态地图组件：使用默认位置 - 纬度: $latitude, 经度: $longitude');
    }

    print('动态地图组件：最终地图参数 - 纬度: $latitude, 经度: $longitude, 缩放: $zoom');

    // 根据平台选择合适的高德地图实现
    if (kIsWeb) {
      print('动态地图组件：Web平台，使用AmapWebWidget');
      // Web平台使用JavaScript API实现
      return AmapWebWidget(
        latitude: latitude,
        longitude: longitude,
        zoom: zoom,
        myLocationEnabled: widget.myLocationEnabled,
        onMyLocationButtonPressed: widget.onMyLocationButtonPressed,
        onMapTap: widget.onMapTap,
        onMapLongPress: widget.onMapLongPress,
      );
    } else {
      print('动态地图组件：移动平台，使用占位符');
      // 移动平台暂时使用占位符
      return Container(
        color: Colors.grey[200],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.map,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                '高德地图',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                '移动平台高德地图组件',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// 构建加载界面
  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 16),
            Text(
              _loadingMessage,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '正在为您选择最佳的地图服务',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建地图服务指示器
  Widget _buildMapServiceIndicator() {
    if (_currentMapService == null) return const SizedBox.shrink();

    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _currentMapService == MapServiceType.googleMaps 
                ? Icons.public 
                : Icons.map,
              size: 16,
              color: Colors.blue,
            ),
            const SizedBox(width: 4),
            Text(
              _mapServiceManager.getMapServiceDisplayName(_currentMapService!),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print('动态地图组件：build方法被调用，当前状态 - 加载中: $_isLoading, 地图服务: $_currentMapService');
    return Stack(
      children: [
        // 地图主体
        if (_isLoading) ...[
          _buildLoadingWidget()
        ] else if (_currentMapService == MapServiceType.googleMaps) ...[
          _buildGoogleMap()
        ] else if (_currentMapService == MapServiceType.amap) ...[
          _buildAmapMap()
        ] else ...[
          _buildLoadingWidget()
        ],

        // 地图服务指示器
        _buildMapServiceIndicator(),

        // 自定义定位按钮
        if (!_isLoading && widget.onMyLocationButtonPressed != null)
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Colors.white,
              foregroundColor: Colors.blue,
              onPressed: widget.onMyLocationButtonPressed,
              child: const Icon(Icons.my_location),
            ),
          ),
      ],
    );
  }

  /// 获取当前地图服务类型
  MapServiceType? get currentMapService => _currentMapService;

  /// 手动切换地图服务
  void switchMapService(MapServiceType newService) {
    if (_currentMapService != newService) {
      setState(() {
        _currentMapService = newService;
      });
      _mapServiceManager.switchMapService(newService);
    }
  }
}
