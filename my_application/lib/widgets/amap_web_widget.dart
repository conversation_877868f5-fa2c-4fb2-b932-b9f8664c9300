import 'package:flutter/material.dart';
import 'dart:html' as html;
import 'dart:ui_web' as ui;
import 'dart:js' as js;

/// 高德地图Web组件
/// 专门用于Web平台的高德地图实现，使用JavaScript API
class AmapWebWidget extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  final double zoom;
  final bool myLocationEnabled;
  final VoidCallback? onMyLocationButtonPressed;
  final Function(double lat, double lng)? onMapTap;
  final Function(double lat, double lng)? onMapLongPress;

  const AmapWebWidget({
    super.key,
    this.latitude,
    this.longitude,
    this.zoom = 14.0,
    this.myLocationEnabled = true,
    this.onMyLocationButtonPressed,
    this.onMapTap,
    this.onMapLongPress,
  });

  @override
  State<AmapWebWidget> createState() => _AmapWebWidgetState();
}

class _AmapWebWidgetState extends State<AmapWebWidget> {
  late String _mapElementId;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _mapElementId = 'amap_${DateTime.now().millisecondsSinceEpoch}';
    _initializeMap();
  }

  void _initializeMap() {
    // 创建地图容器元素
    final mapElement = html.DivElement()
      ..id = _mapElementId
      ..style.width = '100%'
      ..style.height = '100%'
      ..style.backgroundColor = '#f0f0f0';

    // 注册HTML元素
    ui.platformViewRegistry.registerViewFactory(
      _mapElementId,
      (int viewId) => mapElement,
    );

    // 等待DOM元素准备就绪后初始化地图
    print('AmapWebWidget: 开始延迟初始化地图');
    Future.delayed(const Duration(milliseconds: 500), () {
      print('AmapWebWidget: 延迟时间到，开始创建地图');
      _createMap();
    });
  }

  void _loadAmapScript() {
    // 检查高德地图API是否已加载
    if (js.context.hasProperty('AMap')) {
      _createMap();
      return;
    }

    // 动态加载高德地图API
    final script = html.ScriptElement()
      ..src = 'https://webapi.amap.com/maps?v=2.0&key=e02aa654323adbcfdf59a3ae268f586d'
      ..async = true;

    script.onLoad.listen((_) {
      _createMap();
    });

    script.onError.listen((_) {
      setState(() {
        _isLoading = false;
        _errorMessage = '高德地图API加载失败';
      });
    });

    html.document.head!.append(script);
  }

  Future<void> _createMap() async {
    try {
      // 检查高德地图API是否已加载
      if (!js.context.hasProperty('AMap')) {
        setState(() {
          _isLoading = false;
          _errorMessage = '高德地图API未加载，请刷新页面重试';
        });
        return;
      }

      final lat = widget.latitude ?? 39.9042;
      final lng = widget.longitude ?? 116.4074;
      final zoom = widget.zoom;

      print('开始创建高德地图: $_mapElementId, 位置: $lat, $lng, 缩放: $zoom');

      // 设置Flutter回调函数
      js.context['flutterMapTapCallback'] = (double lat, double lng) {
        if (widget.onMapTap != null) {
          widget.onMapTap!(lat, lng);
        }
      };

      js.context['flutterMapLongPressCallback'] = (double lat, double lng) {
        if (widget.onMapLongPress != null) {
          widget.onMapLongPress!(lat, lng);
        }
      };

      // 使用JavaScript创建高德地图 - 按照官方教程标准方式
      js.context.callMethod('eval', ['''
        (function() {
          try {
            console.log('开始初始化高德地图，容器ID: $_mapElementId');

            // 确保AMap已加载
            if (typeof AMap === 'undefined') {
              console.error('AMap未加载');
              window.amapInitError = 'AMap未加载';
              return;
            }

            // 确保容器元素存在
            var container = document.getElementById('$_mapElementId');
            if (!container) {
              console.error('地图容器元素未找到: $_mapElementId');
              window.amapInitError = '地图容器元素未找到';
              return;
            }

            console.log('容器元素找到，开始创建地图实例');
            console.log('地图参数: center=[$lng, $lat], zoom=$zoom');

            // 按照官方教程创建地图实例
            var map = new AMap.Map('$_mapElementId', {
              viewMode: '2D', // 默认使用 2D 模式
              zoom: $zoom, // 地图级别
              center: [$lng, $lat], // 地图中心点
              mapStyle: 'amap://styles/normal',
              lang: 'zh_cn',
              resizeEnable: true,
              rotateEnable: false,
              pitchEnable: false,
              zoomEnable: true,
              dragEnable: true
            });

            console.log('地图实例创建成功');

            // 地图加载完成事件
            map.on('complete', function() {
              console.log('高德地图加载完成');

              // 在地图加载完成后添加控件和标记
              try {
                // 添加地图控件
                map.addControl(new AMap.Scale({
                  position: 'LB'
                }));

                map.addControl(new AMap.ToolBar({
                  position: 'RT'
                }));

                console.log('地图控件添加成功');

                // 添加用户位置标记 - 使用最简单的默认标记
                var marker = new AMap.Marker({
                  position: new AMap.LngLat($lng, $lat),
                  title: '当前位置'
                });
                map.add(marker);
                console.log('地图标记添加成功 - 位置: [$lng, $lat]');

                // 验证标记是否添加成功
                setTimeout(function() {
                  var allOverlays = map.getAllOverlays();
                  console.log('地图上的所有覆盖物数量:', allOverlays.length);
                  console.log('标记对象:', marker);
                  console.log('标记位置:', marker.getPosition());
                }, 1000);

                // 添加位置精度圆圈 - 使用更明显的蓝色
                var circle = new AMap.Circle({
                  center: new AMap.LngLat($lng, $lat),
                  radius: 200, // 200米精度圆，更大更明显
                  strokeColor: '#FF0000', // 红色边框
                  strokeWeight: 3,
                  strokeOpacity: 1.0,
                  fillColor: '#FF0000', // 红色填充
                  fillOpacity: 0.3
                });
                map.add(circle);
                console.log('位置精度圆圈添加成功');

                // 确保地图中心在用户位置
                map.setCenter(new AMap.LngLat($lng, $lat));
                map.setZoom($zoom);
                console.log('地图中心已设置到用户位置');
              } catch (addError) {
                console.error('添加控件或标记失败:', addError);
              }
            });

            // 地图点击事件
            map.on('click', function(e) {
              console.log('地图点击:', e.lnglat.getLat(), e.lnglat.getLng());
              // 调用Flutter的点击回调
              if (window.flutterMapTapCallback) {
                window.flutterMapTapCallback(e.lnglat.getLat(), e.lnglat.getLng());
              }
            });

            // 地图长按事件
            var longPressTimer;
            var isLongPress = false;

            map.on('mousedown', function(e) {
              isLongPress = false;
              longPressTimer = setTimeout(function() {
                isLongPress = true;
                console.log('地图长按:', e.lnglat.getLat(), e.lnglat.getLng());
                // 调用Flutter的长按回调
                if (window.flutterMapLongPressCallback) {
                  window.flutterMapLongPressCallback(e.lnglat.getLat(), e.lnglat.getLng());
                }
              }, 800); // 800ms长按触发
            });

            map.on('mouseup', function(e) {
              clearTimeout(longPressTimer);
            });

            map.on('mousemove', function(e) {
              clearTimeout(longPressTimer);
            });

            // 地图错误事件
            map.on('error', function(e) {
              console.error('地图错误:', e);
              window.amapInitError = '地图运行时错误: ' + JSON.stringify(e);
            });

            console.log('高德地图初始化成功');
            window.amapInitSuccess = true;

          } catch (error) {
            console.error('地图初始化失败:', error);
            window.amapInitError = error.toString();
          }
        })();
      ''']);

      // 等待一段时间检查初始化结果
      await Future.delayed(const Duration(milliseconds: 1000));

      // 检查初始化结果
      try {
        final success = js.context['amapInitSuccess'];
        final error = js.context['amapInitError'];

        if (error != null) {
          setState(() {
            _isLoading = false;
            _errorMessage = '地图初始化失败: $error';
          });
          return;
        }

        if (success == true) {
          setState(() {
            _isLoading = false;
            _errorMessage = null;
          });
        } else {
          setState(() {
            _isLoading = false;
            _errorMessage = '地图初始化状态未知';
          });
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
          _errorMessage = '检查地图状态失败: $e';
        });
      }

    } catch (e) {
      print('高德地图创建失败: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = '地图初始化失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 地图容器
        HtmlElementView(viewType: _mapElementId),
        
        // 加载指示器
        if (_isLoading)
          Container(
            color: Colors.white,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    '正在加载高德地图...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        
        // 错误信息
        if (_errorMessage != null)
          Container(
            color: Colors.white,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                        _isLoading = true;
                      });
                      _loadAmapScript();
                    },
                    child: const Text('重新加载'),
                  ),
                ],
              ),
            ),
          ),
        
        // 我的位置按钮
        if (widget.myLocationEnabled && widget.onMyLocationButtonPressed != null)
          Positioned(
            right: 16,
            bottom: 16,
            child: FloatingActionButton(
              mini: true,
              onPressed: widget.onMyLocationButtonPressed,
              backgroundColor: Colors.white,
              foregroundColor: Colors.blue,
              child: const Icon(Icons.my_location),
            ),
          ),
      ],
    );
  }
}
