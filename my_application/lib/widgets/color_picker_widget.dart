import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

class ColorPickerWidget extends StatefulWidget {
  final Color? selectedColor;
  final Function(Color) onColorSelected;

  const ColorPickerWidget({
    super.key,
    this.selectedColor,
    required this.onColorSelected,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  // 黑白灰度色彩方案
  static const List<Color> _availableColors = [
    Color(0xFF000000), // 黑色
    Color(0xFF212121), // 深灰
    Color(0xFF424242), // 中深灰
    Color(0xFF616161), // 中灰
    Color(0xFF757575), // 浅中灰
    Color(0xFF9E9E9E), // 浅灰
    Color(0xFFBDBDBD), // 很浅灰
    Color(0xFFE0E0E0), // 极浅灰
    Color(0xFFF5F5F5), // 接近白色
    Color(0xFFFFFFFF), // 白色
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('选择颜色'),
      content: SizedBox(
        width: double.maxFinite,
        child: Wrap(
          spacing: 12,
          runSpacing: 12,
          children: _availableColors.map((color) {
            final isSelected = widget.selectedColor == color;
            
            return GestureDetector(
              onTap: () {
                widget.onColorSelected(color);
                Navigator.of(context).pop();
              },
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? AppColors.primaryLight70 : Colors.grey[300]!,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: color == Colors.white || color.computeLuminance() > 0.5
                            ? Colors.black
                            : Colors.white,
                        size: 24,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ],
    );
  }
}
