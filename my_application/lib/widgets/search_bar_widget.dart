import 'package:flutter/material.dart';
import '../screens/search_screen.dart';

class SearchBarWidget extends StatefulWidget {
  final Function(String) onSearch;
  final bool enableNavigation;

  const SearchBarWidget({
    super.key,
    required this.onSearch,
    this.enableNavigation = true,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _navigateToSearch() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SearchScreen(
          initialQuery: _controller.text,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      child: Row(
        children: [
          // 搜索图标
          const Padding(
            padding: EdgeInsets.only(left: 16, right: 8),
            child: Icon(Icons.search, color: Colors.grey, size: 20),
          ),

          // 搜索输入框
          Expanded(
            child: TextField(
              controller: _controller,
              decoration: const InputDecoration(
                hintText: '搜索地点、标签...',
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                hintStyle: TextStyle(color: Colors.grey, fontSize: 16),
              ),
              style: const TextStyle(fontSize: 16),
              onChanged: (value) {
                widget.onSearch(value);
              },
              onTap: () {
                if (widget.enableNavigation) {
                  _navigateToSearch();
                }
              },
              readOnly: widget.enableNavigation,
            ),
          ),
          
          // 清除按钮（仅在有文本时显示）
          if (_controller.text.isNotEmpty)
            IconButton(
              onPressed: () {
                _controller.clear();
                widget.onSearch('');
              },
              icon: const Icon(Icons.clear, size: 20),
              color: Colors.grey[600],
            ),
        ],
      ),
    );
  }
}
