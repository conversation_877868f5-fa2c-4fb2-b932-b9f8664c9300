import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'dart:math' as math;

import '../services/mobile_features_service.dart';

/// 移动端下拉刷新组件
class MobilePullToRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double threshold;
  final Color? backgroundColor;
  final Color? indicatorColor;
  final bool enableVibration;

  const MobilePullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.threshold = 80.0,
    this.backgroundColor,
    this.indicatorColor,
    this.enableVibration = true,
  });

  @override
  State<MobilePullToRefresh> createState() => _MobilePullToRefreshState();
}

class _MobilePullToRefreshState extends State<MobilePullToRefresh>
    with TickerProviderStateMixin {
  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();
  
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late Animation<double> _animation;
  late Animation<double> _rotationAnimation;
  
  double _pullDistance = 0.0;
  bool _isRefreshing = false;
  bool _canRefresh = false;
  bool _hasTriggeredVibration = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
      _canRefresh = false;
    });

    // 震动反馈
    if (widget.enableVibration) {
      await _mobileFeatures.combinedFeedback(
        vibrationPattern: VibrationPattern.medium,
      );
    }

    // 开始旋转动画
    _rotationController.repeat();

    try {
      await widget.onRefresh();
    } finally {
      if (mounted) {
        _rotationController.stop();
        _rotationController.reset();
        
        await _animationController.reverse();
        
        setState(() {
          _isRefreshing = false;
          _pullDistance = 0.0;
          _hasTriggeredVibration = false;
        });
      }
    }
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (_isRefreshing) return;

    final delta = details.delta.dy;
    if (delta > 0) {
      setState(() {
        _pullDistance = math.min(_pullDistance + delta * 0.5, widget.threshold * 1.5);
      });

      // 检查是否达到刷新阈值
      if (_pullDistance >= widget.threshold && !_canRefresh) {
        setState(() {
          _canRefresh = true;
        });
        
        // 震动反馈
        if (widget.enableVibration && !_hasTriggeredVibration) {
          _mobileFeatures.combinedFeedback(
            vibrationPattern: VibrationPattern.light,
          );
          _hasTriggeredVibration = true;
        }
        
        _animationController.forward();
      } else if (_pullDistance < widget.threshold && _canRefresh) {
        setState(() {
          _canRefresh = false;
          _hasTriggeredVibration = false;
        });
        
        _animationController.reverse();
      }
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    if (_isRefreshing) return;

    if (_canRefresh) {
      _handleRefresh();
    } else {
      // 回弹动画
      _animationController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _pullDistance = 0.0;
            _hasTriggeredVibration = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.backgroundColor ?? theme.scaffoldBackgroundColor;
    final indicatorColor = widget.indicatorColor ?? theme.primaryColor;

    return GestureDetector(
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
      child: Stack(
        children: [
          // 主要内容
          Transform.translate(
            offset: Offset(0, _pullDistance),
            child: widget.child,
          ),
          
          // 下拉刷新指示器
          if (_pullDistance > 0)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Transform.translate(
                offset: Offset(0, _pullDistance - widget.threshold),
                child: Container(
                  height: widget.threshold,
                  color: backgroundColor,
                  child: _buildRefreshIndicator(indicatorColor),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRefreshIndicator(Color indicatorColor) {
    return Center(
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 刷新图标
              AnimatedBuilder(
                animation: _rotationAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _isRefreshing ? _rotationAnimation.value : 0,
                    child: HeroIcon(
                      _isRefreshing ? HeroIcons.arrowPath : HeroIcons.chevronDown,
                      size: 20,
                      color: indicatorColor.withOpacity(0.8),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 8),
              
              // 状态文本
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Text(
                  _getStatusText(),
                  key: ValueKey(_getStatusText()),
                  style: TextStyle(
                    fontSize: 12,
                    color: indicatorColor.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  String _getStatusText() {
    if (_isRefreshing) {
      return '刷新中...';
    } else if (_canRefresh) {
      return '松开刷新';
    } else {
      return '下拉刷新';
    }
  }
}

/// 简化版下拉刷新组件
class SimplePullToRefresh extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final bool enableVibration;

  const SimplePullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.enableVibration = true,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        if (enableVibration) {
          final mobileFeatures = MobileFeaturesService();
          await mobileFeatures.combinedFeedback(
            vibrationPattern: VibrationPattern.light,
          );
        }
        await onRefresh();
      },
      child: child,
    );
  }
}

/// 自定义下拉刷新配置
class PullToRefreshConfig {
  final double threshold;
  final Color? backgroundColor;
  final Color? indicatorColor;
  final bool enableVibration;
  final Duration animationDuration;

  const PullToRefreshConfig({
    this.threshold = 80.0,
    this.backgroundColor,
    this.indicatorColor,
    this.enableVibration = true,
    this.animationDuration = const Duration(milliseconds: 200),
  });
}

/// 下拉刷新状态枚举
enum PullToRefreshState {
  idle,       // 空闲状态
  pulling,    // 下拉中
  canRefresh, // 可以刷新
  refreshing, // 刷新中
  completed,  // 刷新完成
}

/// 下拉刷新事件数据
class PullToRefreshEvent {
  final PullToRefreshState state;
  final double pullDistance;
  final double threshold;
  final bool canRefresh;

  const PullToRefreshEvent({
    required this.state,
    required this.pullDistance,
    required this.threshold,
    required this.canRefresh,
  });

  double get progress => (pullDistance / threshold).clamp(0.0, 1.0);
  
  bool get isOverThreshold => pullDistance >= threshold;
}
