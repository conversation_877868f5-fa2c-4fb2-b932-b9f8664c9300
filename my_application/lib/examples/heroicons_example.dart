import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import '../constants/app_colors.dart';

/// Heroicons 使用示例
/// 展示如何在 FavsAny 应用中使用 Heroicons
class HeroiconsExample extends StatelessWidget {
  const HeroiconsExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Heroicons 示例'),
        leading: IconButton(
          icon: const HeroIcon(
            HeroIcons.arrowLeft,
            style: HeroIconStyle.outline,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              '导航图标',
              [
                _buildIconItem('菜单', HeroIcons.bars3),
                _buildIconItem('搜索', HeroIcons.magnifyingGlass),
                _buildIconItem('关闭', HeroIcons.xMark),
                _buildIconItem('返回', HeroIcons.arrowLeft),
                _buildIconItem('前进', HeroIcons.arrowRight),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              '地图相关图标',
              [
                _buildIconItem('地图标记', HeroIcons.mapPin),
                _buildIconItem('位置', HeroIcons.mapPin),
                _buildIconItem('导航', HeroIcons.map),
                _buildIconItem('全球', HeroIcons.globeAlt),
                _buildIconItem('指南针', HeroIcons.compass),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              '功能图标',
              [
                _buildIconItem('收藏', HeroIcons.heart),
                _buildIconItem('添加', HeroIcons.plus),
                _buildIconItem('编辑', HeroIcons.pencil),
                _buildIconItem('删除', HeroIcons.trash),
                _buildIconItem('分享', HeroIcons.share),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              '设置图标',
              [
                _buildIconItem('设置', HeroIcons.cog6Tooth),
                _buildIconItem('信息', HeroIcons.informationCircle),
                _buildIconItem('用户', HeroIcons.user),
                _buildIconItem('通知', HeroIcons.bell),
                _buildIconItem('安全', HeroIcons.lockClosed),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              '分类图标',
              [
                _buildIconItem('餐厅', HeroIcons.buildingStorefront),
                _buildIconItem('咖啡', HeroIcons.cup),
                _buildIconItem('书店', HeroIcons.bookOpen),
                _buildIconItem('购物', HeroIcons.shoppingBag),
                _buildIconItem('工作', HeroIcons.briefcase),
              ],
            ),
            const SizedBox(height: 24),
            _buildStyleComparison(),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> icons) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryLight70,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: icons,
        ),
      ],
    );
  }

  Widget _buildIconItem(String label, HeroIcons icon) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppColors.primaryLight10,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primaryLight30,
              width: 1,
            ),
          ),
          child: HeroIcon(
            icon,
            style: HeroIconStyle.outline,
            color: AppColors.primaryLight70,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStyleComparison() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '图标样式对比',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryLight70,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStyleExample('Outline', HeroIconStyle.outline),
            _buildStyleExample('Solid', HeroIconStyle.solid),
            _buildStyleExample('Mini', HeroIconStyle.mini),
          ],
        ),
      ],
    );
  }

  Widget _buildStyleExample(String styleName, HeroIconStyle style) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primaryLight10,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primaryLight30,
              width: 1,
            ),
          ),
          child: HeroIcon(
            HeroIcons.heart,
            style: style,
            color: AppColors.primaryLight70,
            size: 32,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          styleName,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
