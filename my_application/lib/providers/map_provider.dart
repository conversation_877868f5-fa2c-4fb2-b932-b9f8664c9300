import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/marker_model.dart';
import '../models/category_model.dart';
import '../services/marker_service.dart';

class MapProvider extends ChangeNotifier {
  final MarkerService _markerService = MarkerService();
  
  // 地图控制器
  GoogleMapController? _mapController;
  GoogleMapController? get mapController => _mapController;
  
  // 标记点数据
  List<MarkerModel> _markers = [];
  List<MarkerModel> get markers => _markers;
  
  List<MarkerModel> _filteredMarkers = [];
  List<MarkerModel> get filteredMarkers => _filteredMarkers;
  
  // 分类数据
  List<CategoryModel> _categories = [];
  List<CategoryModel> get categories => _categories;
  
  // 当前选中的分类
  String? _selectedCategory;
  String? get selectedCategory => _selectedCategory;
  
  // 搜索关键词
  String _searchQuery = '';
  String get searchQuery => _searchQuery;
  
  // 地图状态
  bool _isMapReady = false;
  bool get isMapReady => _isMapReady;
  
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  
  // 当前位置
  LatLng? _currentLocation;
  LatLng? get currentLocation => _currentLocation;
  
  // 选中的标记点
  MarkerModel? _selectedMarker;
  MarkerModel? get selectedMarker => _selectedMarker;

  // 初始化
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      await loadCategories();
      await loadMarkers();
    } catch (e) {
      print('Error initializing MapProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 设置地图控制器
  void setMapController(GoogleMapController controller) {
    _mapController = controller;
    _isMapReady = true;
    notifyListeners();
  }

  // 加载标记点
  Future<void> loadMarkers() async {
    try {
      _markers = await _markerService.getAllMarkers();
      _applyFilters();
      notifyListeners();
      
      // 更新地图上的标记点
      if (_isMapReady) {
        await _updateMapMarkers();
      }
    } catch (e) {
      print('Error loading markers: $e');
    }
  }

  // 加载分类
  Future<void> loadCategories() async {
    try {
      _categories = await _markerService.getAllCategories();
      notifyListeners();
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  // 添加标记点
  Future<void> addMarker(MarkerModel marker) async {
    try {
      final id = await _markerService.createMarker(marker);
      final newMarker = marker.copyWith(id: id);
      _markers.add(newMarker);
      _applyFilters();
      notifyListeners();
      
      // Google Maps 会自动更新标记点
    } catch (e) {
      print('Error adding marker: $e');
      rethrow;
    }
  }

  // 更新标记点
  Future<void> updateMarker(MarkerModel marker) async {
    try {
      await _markerService.updateMarker(marker);
      final index = _markers.indexWhere((m) => m.id == marker.id);
      if (index != -1) {
        _markers[index] = marker;
        _applyFilters();
        notifyListeners();
        
        if (_isMapReady) {
          await _updateMapMarkers();
        }
      }
    } catch (e) {
      print('Error updating marker: $e');
      rethrow;
    }
  }

  // 删除标记点
  Future<void> deleteMarker(int id) async {
    try {
      await _markerService.deleteMarker(id);
      _markers.removeWhere((m) => m.id == id);
      _applyFilters();
      notifyListeners();
      
      if (_isMapReady) {
        await _updateMapMarkers();
      }
    } catch (e) {
      print('Error deleting marker: $e');
      rethrow;
    }
  }

  // 设置分类筛选
  void setSelectedCategory(String? category) {
    _selectedCategory = category;
    _applyFilters();
    notifyListeners();
  }

  // 设置搜索关键词
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  // 应用筛选条件
  void _applyFilters() {
    _filteredMarkers = _markers.where((marker) {
      bool matchesCategory = _selectedCategory == null || 
                           marker.category == _selectedCategory;
      
      bool matchesSearch = _searchQuery.isEmpty ||
                          marker.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                          (marker.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                          marker.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()));
      
      return matchesCategory && matchesSearch;
    }).toList();
  }

  // 更新地图上的标记点
  Future<void> _updateMapMarkers() async {
    if (_mapController == null) return;

    // Google Maps 通过 markers 属性直接管理标记点
    // 不需要手动添加/删除标记点
    notifyListeners();
  }

  // Google Maps 不需要手动添加标记点方法

  // 设置当前位置
  void setCurrentLocation(LatLng location) {
    _currentLocation = location;
    notifyListeners();
  }

  // 设置选中的标记点
  void setSelectedMarker(MarkerModel? marker) {
    _selectedMarker = marker;
    notifyListeners();
  }

  // 清除选中的标记点
  void clearSelectedMarker() {
    _selectedMarker = null;
    notifyListeners();
  }

  // 移动到当前位置 (Google Maps 通过 animateCamera 实现)
  Future<void> moveToCurrentLocation() async {
    if (_mapController == null || _currentLocation == null) return;

    try {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLng(_currentLocation!),
      );
    } catch (e) {
      // 忽略错误
    }
  }

  // 移动到指定标记点
  Future<void> moveToMarker(MarkerModel marker) async {
    if (_mapController == null) return;

    try {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLng(LatLng(marker.latitude, marker.longitude)),
      );
      _selectedMarker = marker;
      notifyListeners();
    } catch (e) {
      // 忽略错误
    }
  }

  // 分类管理方法
  Future<void> addCategory(CategoryModel category) async {
    try {
      final id = await _markerService.createCategory(category);
      final newCategory = category.copyWith(id: id);
      _categories.add(newCategory);
      notifyListeners();
    } catch (e) {
      print('Error adding category: $e');
      rethrow;
    }
  }

  Future<void> updateCategory(CategoryModel category) async {
    try {
      await _markerService.updateCategory(category);
      final index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        notifyListeners();
      }
    } catch (e) {
      print('Error updating category: $e');
      rethrow;
    }
  }

  Future<void> deleteCategory(int id) async {
    try {
      await _markerService.deleteCategory(id);
      _categories.removeWhere((c) => c.id == id);
      notifyListeners();
    } catch (e) {
      print('Error deleting category: $e');
      rethrow;
    }
  }

  // 获取统计信息
  Future<Map<String, int>> getStatistics() async {
    return await _markerService.getStatistics();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
