import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';

import '../constants/app_constants.dart';
import '../constants/app_colors.dart';
import '../providers/map_provider.dart';
import '../services/marker_service.dart';
import 'category_management_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isDarkMode = false;
  bool _defaultPrivacy = false;
  bool _autoBackup = true;
  bool _locationPermission = true;
  String _mapType = 'standard';
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool(AppConstants.keyThemeMode) ?? false;
      _defaultPrivacy = prefs.getBool(AppConstants.keyDefaultPrivacy) ?? false;
      _autoBackup = prefs.getBool(AppConstants.keyAutoBackup) ?? true;
      _locationPermission = prefs.getBool(AppConstants.keyLocationPermission) ?? true;
      _mapType = prefs.getString(AppConstants.keyMapType) ?? 'standard';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
      ),
      body: ListView(
        children: [
          // 外观设置
          _buildSectionHeader('外观'),
          _buildSwitchTile(
            title: '深色模式',
            subtitle: '使用深色主题',
            value: _isDarkMode,
            onChanged: _toggleDarkMode,
            icon: Icons.dark_mode,
          ),
          
          // 地图设置
          _buildSectionHeader('地图'),
          _buildListTile(
            title: '地图类型',
            subtitle: _getMapTypeDisplayName(_mapType),
            icon: Icons.map,
            onTap: _showMapTypeDialog,
          ),
          _buildSwitchTile(
            title: '位置权限',
            subtitle: '允许获取当前位置',
            value: _locationPermission,
            onChanged: _toggleLocationPermission,
            icon: Icons.location_on,
          ),
          
          // 隐私设置
          _buildSectionHeader('隐私'),
          _buildSwitchTile(
            title: '默认公开',
            subtitle: '新标记默认设为公开',
            value: _defaultPrivacy,
            onChanged: _toggleDefaultPrivacy,
            icon: Icons.public,
          ),
          
          // 数据管理
          _buildSectionHeader('数据管理'),
          _buildSwitchTile(
            title: '自动备份',
            subtitle: '定期备份数据到本地',
            value: _autoBackup,
            onChanged: _toggleAutoBackup,
            icon: Icons.backup,
          ),
          _buildListTile(
            title: '分类管理',
            subtitle: '管理标记分类',
            icon: Icons.category,
            onTap: _openCategoryManagement,
          ),
          _buildListTile(
            title: '导出数据',
            subtitle: '导出所有标记数据',
            icon: Icons.download,
            onTap: _exportData,
          ),
          _buildListTile(
            title: '导入数据',
            subtitle: '从文件导入标记数据',
            icon: Icons.upload,
            onTap: _importData,
          ),
          _buildListTile(
            title: '清除数据',
            subtitle: '删除所有标记数据',
            icon: Icons.delete_forever,
            onTap: _clearAllData,
            textColor: AppColors.error,
          ),
          
          // 关于
          _buildSectionHeader('关于'),
          _buildListTile(
            title: '版本信息',
            subtitle: 'v${AppConstants.appVersion}',
            icon: Icons.info,
            onTap: _showAboutDialog,
          ),
          _buildListTile(
            title: '使用协议',
            subtitle: '查看使用条款',
            icon: Icons.description,
            onTap: _showTermsDialog,
          ),
          _buildListTile(
            title: '反馈建议',
            subtitle: '发送反馈或建议',
            icon: Icons.feedback,
            onTap: _sendFeedback,
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      secondary: Icon(icon),
    );
  }

  Widget _buildListTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(color: textColor),
      ),
      subtitle: Text(subtitle),
      leading: Icon(icon, color: textColor),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Future<void> _toggleDarkMode(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyThemeMode, value);
    setState(() {
      _isDarkMode = value;
    });
    // TODO: 实现主题切换
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('主题切换功能即将实现')),
    );
  }

  Future<void> _toggleDefaultPrivacy(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyDefaultPrivacy, value);
    setState(() {
      _defaultPrivacy = value;
    });
  }

  Future<void> _toggleAutoBackup(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyAutoBackup, value);
    setState(() {
      _autoBackup = value;
    });
  }

  Future<void> _toggleLocationPermission(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyLocationPermission, value);
    setState(() {
      _locationPermission = value;
    });
  }

  void _showMapTypeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择地图类型'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('标准地图'),
              value: 'standard',
              groupValue: _mapType,
              onChanged: (value) => _setMapType(value!),
            ),
            RadioListTile<String>(
              title: const Text('卫星地图'),
              value: 'satellite',
              groupValue: _mapType,
              onChanged: (value) => _setMapType(value!),
            ),
            RadioListTile<String>(
              title: const Text('混合地图'),
              value: 'hybrid',
              groupValue: _mapType,
              onChanged: (value) => _setMapType(value!),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Future<void> _setMapType(String type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyMapType, type);
    setState(() {
      _mapType = type;
    });
  }

  String _getMapTypeDisplayName(String type) {
    switch (type) {
      case 'satellite':
        return '卫星地图';
      case 'hybrid':
        return '混合地图';
      default:
        return '标准地图';
    }
  }

  void _openCategoryManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CategoryManagementScreen(),
      ),
    );
  }

  Future<void> _exportData() async {
    try {
      final markerService = MarkerService();
      final data = await markerService.exportData();
      
      // TODO: 实现文件导出
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('导出了 ${data.length} 条数据')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('导出失败: $e')),
      );
    }
  }

  void _importData() {
    // TODO: 实现数据导入
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('数据导入功能即将实现')),
    );
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除所有数据'),
        content: const Text('此操作将删除所有标记数据，且无法恢复。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // TODO: 实现清除所有数据
        if (mounted) {
          context.read<MapProvider>().loadMarkers();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('所有数据已清除')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('清除失败: $e')),
          );
        }
      }
    }
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appNameChinese,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: const Icon(Icons.map, size: 48),
      children: [
        const Text('一个基于地理位置的个人兴趣收藏工具'),
        const SizedBox(height: 16),
        const Text('功能特点：'),
        const Text('• 隐私优先，本地存储'),
        const Text('• 支持多种分类和标签'),
        const Text('• 可选的云端同步'),
        const Text('• Material Design 3 设计'),
      ],
    );
  }

  void _showTermsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('使用协议'),
        content: const SingleChildScrollView(
          child: Text(
            '1. 本应用尊重用户隐私，默认所有数据存储在本地设备。\n\n'
            '2. 用户可自主选择是否启用云端同步功能。\n\n'
            '3. 应用不会收集用户的个人敏感信息。\n\n'
            '4. 用户对自己创建的内容拥有完全控制权。\n\n'
            '5. 本应用仅供个人使用，请勿用于商业用途。',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  void _sendFeedback() {
    // TODO: 实现反馈功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('反馈功能即将实现')),
    );
  }
}
