import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/category_model.dart';
import '../providers/map_provider.dart';
import '../constants/app_colors.dart';
import '../widgets/category_icon_picker.dart';
import '../widgets/color_picker_widget.dart';

class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  State<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类管理'),
        actions: [
          IconButton(
            onPressed: _addNewCategory,
            icon: const Icon(Icons.add),
            tooltip: '添加分类',
          ),
        ],
      ),
      body: Consumer<MapProvider>(
        builder: (context, mapProvider, child) {
          if (mapProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final categories = mapProvider.categories;

          return ReorderableListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: categories.length,
            onReorder: (oldIndex, newIndex) {
              _reorderCategories(mapProvider, oldIndex, newIndex);
            },
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryItem(category, index);
            },
          );
        },
      ),
    );
  }

  Widget _buildCategoryItem(CategoryModel category, int index) {
    return Card(
      key: ValueKey(category.id),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: category.color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getIconData(category.icon),
            color: category.color,
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          category.isDefault ? '系统默认' : '自定义分类',
          style: TextStyle(
            color: category.isDefault ? AppColors.textSecondary : AppColors.primary,
            fontSize: 12,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _editCategory(category),
              icon: const Icon(Icons.edit),
              tooltip: '编辑',
            ),
            if (!category.isDefault)
              IconButton(
                onPressed: () => _deleteCategory(category),
                icon: const Icon(Icons.delete),
                color: AppColors.error,
                tooltip: '删除',
              ),
            const Icon(Icons.drag_handle),
          ],
        ),
      ),
    );
  }

  void _addNewCategory() {
    _showCategoryDialog();
  }

  void _editCategory(CategoryModel category) {
    _showCategoryDialog(category: category);
  }

  void _showCategoryDialog({CategoryModel? category}) {
    showDialog(
      context: context,
      builder: (context) => _CategoryDialog(
        category: category,
        onSave: (newCategory) async {
          final mapProvider = context.read<MapProvider>();
          try {
            if (category == null) {
              // 添加新分类
              await mapProvider.addCategory(newCategory);
            } else {
              // 更新分类
              await mapProvider.updateCategory(newCategory);
            }
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(category == null ? '分类已添加' : '分类已更新'),
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('操作失败: $e')),
              );
            }
          }
        },
      ),
    );
  }

  Future<void> _deleteCategory(CategoryModel category) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除分类'),
        content: Text('确定要删除"${category.name}"分类吗？\n\n此操作将影响使用该分类的所有标记点。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true && category.id != null) {
      try {
        final mapProvider = context.read<MapProvider>();
        await mapProvider.deleteCategory(category.id!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('分类已删除')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('删除失败: $e')),
          );
        }
      }
    }
  }

  void _reorderCategories(MapProvider mapProvider, int oldIndex, int newIndex) {
    // TODO: 实现分类重排序
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分类重排序功能即将实现')),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant':
        return Icons.restaurant;
      case 'local_cafe':
        return Icons.local_cafe;
      case 'menu_book':
        return Icons.menu_book;
      case 'place':
        return Icons.place;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'work':
        return Icons.work;
      case 'flight':
        return Icons.flight;
      case 'more_horiz':
        return Icons.more_horiz;
      default:
        return Icons.place;
    }
  }
}

class _CategoryDialog extends StatefulWidget {
  final CategoryModel? category;
  final Function(CategoryModel) onSave;

  const _CategoryDialog({
    this.category,
    required this.onSave,
  });

  @override
  State<_CategoryDialog> createState() => _CategoryDialogState();
}

class _CategoryDialogState extends State<_CategoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  String _selectedIcon = 'place';
  Color _selectedColor = AppColors.primary;

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _selectedIcon = widget.category!.icon;
      _selectedColor = widget.category!.color;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.category == null ? '添加分类' : '编辑分类'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 名称输入
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '分类名称',
                hintText: '输入分类名称',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入分类名称';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // 图标选择
            Row(
              children: [
                const Text('图标：'),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: _selectIcon,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _selectedColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _selectedColor),
                    ),
                    child: Icon(
                      _getIconData(_selectedIcon),
                      color: _selectedColor,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 颜色选择
            Row(
              children: [
                const Text('颜色：'),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: _selectColor,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _selectedColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        FilledButton(
          onPressed: _saveCategory,
          child: const Text('保存'),
        ),
      ],
    );
  }

  void _selectIcon() {
    // TODO: 实现图标选择器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('图标选择器即将实现')),
    );
  }

  void _selectColor() {
    // TODO: 实现颜色选择器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('颜色选择器即将实现')),
    );
  }

  void _saveCategory() {
    if (!_formKey.currentState!.validate()) return;

    final category = CategoryModel(
      id: widget.category?.id,
      name: _nameController.text.trim(),
      icon: _selectedIcon,
      color: _selectedColor,
      sortOrder: widget.category?.sortOrder ?? 0,
      isDefault: widget.category?.isDefault ?? false,
      createdAt: widget.category?.createdAt ?? DateTime.now(),
    );

    widget.onSave(category);
    Navigator.of(context).pop();
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant':
        return Icons.restaurant;
      case 'local_cafe':
        return Icons.local_cafe;
      case 'menu_book':
        return Icons.menu_book;
      case 'place':
        return Icons.place;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'work':
        return Icons.work;
      case 'flight':
        return Icons.flight;
      case 'more_horiz':
        return Icons.more_horiz;
      default:
        return Icons.place;
    }
  }
}
