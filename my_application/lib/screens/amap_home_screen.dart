import 'package:flutter/material.dart';
import 'package:heroicons/heroicons.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:geolocator/geolocator.dart';


import '../constants/app_colors.dart';
import '../widgets/dynamic_map_widget.dart';
import '../services/map_service_manager.dart';
import 'favorites_list_screen.dart';
import 'settings_screen.dart';

class AmapHomeScreen extends StatefulWidget {
  const AmapHomeScreen({super.key});

  @override
  State<AmapHomeScreen> createState() => _AmapHomeScreenState();
}

class _AmapHomeScreenState extends State<AmapHomeScreen> with TickerProviderStateMixin {
  dynamic _mapController; // 可以是GoogleMapController或AMapController
  bool _isPermissionGranted = false;
  final TextEditingController _searchController = TextEditingController();
  String _locationInfo = '正在获取位置信息...';
  Position? _currentPosition;
  Set<gmaps.Marker> _googleMarkers = {};
  final MapServiceManager _mapServiceManager = MapServiceManager();
  gmaps.CameraPosition? _currentCameraPosition;

  // 默认位置（北京）
  static const gmaps.CameraPosition _defaultGooglePosition = gmaps.CameraPosition(
    target: gmaps.LatLng(39.9042, 116.4074),
    zoom: 14.0,
  );

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    print('地图服务初始化开始');
    await _requestLocationPermission();

    // 初始化地图服务（这会自动获取位置信息）
    await _mapServiceManager.initializeMapService();

    // 如果地图服务管理器获取到了位置信息，使用它来更新地图
    if (_mapServiceManager.userPosition != null) {
      final position = _mapServiceManager.userPosition!;
      setState(() {
        _currentPosition = position;
        _locationInfo = '当前位置: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}';

        // 更新地图中心位置
        _currentCameraPosition = gmaps.CameraPosition(
          target: gmaps.LatLng(position.latitude, position.longitude),
          zoom: 16.0,
        );

        // 创建Google Maps标记
        _googleMarkers = {
          gmaps.Marker(
            markerId: const gmaps.MarkerId('current_location'),
            position: gmaps.LatLng(position.latitude, position.longitude),
            infoWindow: const gmaps.InfoWindow(title: '当前位置'),
          ),
        };
      });
    } else if (_isPermissionGranted) {
      // 如果地图服务管理器没有获取到位置，手动获取
      await _getCurrentLocation();
    }
  }

  Future<void> _requestLocationPermission() async {
    try {
      // 检查定位服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _locationInfo = '定位服务未启用，请在设置中开启';
        });
        return;
      }

      // 检查权限状态
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _locationInfo = '定位权限被拒绝';
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _locationInfo = '定位权限被永久拒绝，请在设置中手动开启';
        });
        return;
      }

      setState(() {
        _isPermissionGranted = true;
        _locationInfo = '定位权限已获取，正在获取位置...';
      });
    } catch (e) {
      setState(() {
        _locationInfo = '获取定位权限失败: $e';
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _currentPosition = position;
        _locationInfo = '当前位置: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}';

        // 更新地图中心位置
        _currentCameraPosition = gmaps.CameraPosition(
          target: gmaps.LatLng(position.latitude, position.longitude),
          zoom: 16.0, // 使用更高的缩放级别显示详细位置
        );

        // 创建Google Maps标记
        _googleMarkers = {
          gmaps.Marker(
            markerId: const gmaps.MarkerId('current_location'),
            position: gmaps.LatLng(position.latitude, position.longitude),
            infoWindow: const gmaps.InfoWindow(title: '当前位置'),
          ),
        };
      });

      // 如果地图控制器可用，移动到当前位置
      if (_mapController != null) {
        await _mapController!.animateCamera(
          gmaps.CameraUpdate.newCameraPosition(_currentCameraPosition!),
        );
      }
    } catch (e) {
      setState(() {
        _locationInfo = '获取位置失败: $e';
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 显示添加收藏对话框
  void _showAddFavoriteDialog(double latitude, double longitude) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '添加收藏位置',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 位置信息显示
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '位置坐标:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '纬度: ${latitude.toStringAsFixed(6)}',
                        style: const TextStyle(fontSize: 13),
                      ),
                      Text(
                        '经度: ${longitude.toStringAsFixed(6)}',
                        style: const TextStyle(fontSize: 13),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 收藏名称输入
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: '收藏名称',
                    hintText: '请输入收藏位置的名称',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.bookmark),
                  ),
                  maxLength: 50,
                ),
                const SizedBox(height: 16),

                // 描述输入
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: '描述 (可选)',
                    hintText: '添加一些描述信息',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                  maxLength: 200,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                final name = nameController.text.trim();
                if (name.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('请输入收藏名称'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                  return;
                }

                // TODO: 保存收藏到本地存储
                _saveFavoriteLocation(
                  name: name,
                  description: descriptionController.text.trim(),
                  latitude: latitude,
                  longitude: longitude,
                );

                Navigator.of(context).pop();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('已添加收藏: $name'),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
  }

  /// 保存收藏位置到本地存储
  void _saveFavoriteLocation({
    required String name,
    required String description,
    required double latitude,
    required double longitude,
  }) {
    // TODO: 实现本地存储逻辑
    print('保存收藏位置: $name, 描述: $description, 坐标: ($latitude, $longitude)');

    // 这里可以使用 SharedPreferences 或其他本地存储方案
    // 暂时只打印日志
  }

  // 顶部导航栏
  Widget _buildTopNavigationBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 位置信息
          Expanded(
            child: Row(
              children: [
                HeroIcon(
                  HeroIcons.mapPin,
                  style: HeroIconStyle.solid,
                  size: 16,
                  color: Colors.blue.shade600,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _locationInfo.isNotEmpty ? _locationInfo : '上海, 中国',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // 用户头像
          Builder(
            builder: (context) => GestureDetector(
              onTap: () => Scaffold.of(context).openDrawer(),
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                ),
                child: const HeroIcon(
                  HeroIcons.user,
                  style: HeroIconStyle.outline,
                  size: 20,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 搜索栏
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(24),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  const SizedBox(width: 16),
                  HeroIcon(
                    HeroIcons.magnifyingGlass,
                    style: HeroIconStyle.outline,
                    size: 20,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: '搜索收藏地点...',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 16,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: HeroIcon(
              HeroIcons.adjustmentsHorizontal,
              style: HeroIconStyle.outline,
              size: 20,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // 底部导航栏
  Widget _buildBottomNavigationBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: Colors.blue.shade600,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  HeroIcon(
                    HeroIcons.home,
                    style: HeroIconStyle.solid,
                    size: 20,
                    color: Colors.white,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '首页',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: HeroIcon(
              HeroIcons.heart,
              style: HeroIconStyle.outline,
              size: 20,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // 顶部导航栏
            _buildTopNavigationBar(),

            // 搜索栏
            _buildSearchBar(),

            // 地图区域
            Expanded(
              child: Stack(
                children: [
                  // 地图主体
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: DynamicMapWidget(
                        onMapCreated: (controller) {
                          _mapController = controller;
                        },
                        googleMarkers: _googleMarkers,
                        initialCameraPosition: _currentCameraPosition ?? _defaultGooglePosition,
                        myLocationEnabled: _isPermissionGranted,
                        onMyLocationButtonPressed: _getCurrentLocation,
                        onMapTap: (lat, lng) {
                          print('地图点击: 纬度=$lat, 经度=$lng');
                        },
                        onMapLongPress: (lat, lng) {
                          print('地图长按: 纬度=$lat, 经度=$lng');
                          _showAddFavoriteDialog(lat, lng);
                        },
                      ),
                    ),
                  ),

                  // 长按提示
                  Positioned(
                    bottom: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.black87,
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: const Text(
                          '长按地图添加收藏点',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 底部导航栏
            _buildBottomNavigationBar(),
          ],
        ),
      ),
      drawer: _buildDrawer(),
    );
  }





  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryLight70,
                  AppColors.primaryLight80,
                ],
              ),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                HeroIcon(
                  HeroIcons.heart,
                  style: HeroIconStyle.solid,
                  size: 48,
                  color: Colors.white,
                ),
                SizedBox(height: 16),
                Text(
                  'FavsAny',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '我的喜好地图',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const HeroIcon(
              HeroIcons.heart,
              style: HeroIconStyle.outline,
            ),
            title: const Text('我的收藏'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FavoritesListScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const HeroIcon(
              HeroIcons.cog6Tooth,
              style: HeroIconStyle.outline,
            ),
            title: const Text('设置'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
