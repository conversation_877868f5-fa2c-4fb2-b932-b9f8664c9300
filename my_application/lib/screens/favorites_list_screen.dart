import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../models/marker_model.dart';
import '../providers/map_provider.dart';
import '../constants/app_colors.dart';
import '../widgets/marker_list_item.dart';
import '../widgets/sort_filter_bottom_sheet.dart';
import '../widgets/collections/swipeable_list_item.dart';
import '../widgets/mobile_share_sheet.dart';
import '../widgets/mobile_pull_to_refresh.dart';
import '../widgets/dialogs/marker_detail_dialog.dart';
import '../services/share_service.dart';
import '../services/mobile_features_service.dart';
import 'add_edit_marker_screen.dart';
import 'marker_detail_screen.dart';

enum SortOption {
  dateCreated,
  dateUpdated,
  title,
  category,
  rating,
}

enum FilterOption {
  all,
  public,
  private,
  withImages,
  withRating,
}

class FavoritesListScreen extends StatefulWidget {
  const FavoritesListScreen({super.key});

  @override
  State<FavoritesListScreen> createState() => _FavoritesListScreenState();
}

class _FavoritesListScreenState extends State<FavoritesListScreen> {
  final ShareService _shareService = ShareService();
  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();
  final TextEditingController _searchController = TextEditingController();
  final Set<int> _selectedItems = <int>{};
  
  SortOption _sortOption = SortOption.dateCreated;
  bool _sortAscending = false;
  FilterOption _filterOption = FilterOption.all;
  String _searchQuery = '';
  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
    _initializeMobileFeatures();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MapProvider>().loadMarkers();
    });
  }

  Future<void> _initializeMobileFeatures() async {
    await _mobileFeatures.initialize();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Consumer<MapProvider>(
        builder: (context, mapProvider, child) {
          if (mapProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final filteredMarkers = _getFilteredAndSortedMarkers(mapProvider.markers);

          if (filteredMarkers.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // 搜索栏
              _buildSearchBar(),
              
              // 统计信息
              _buildStatsBar(filteredMarkers.length, mapProvider.markers.length),
              
              // 列表
              Expanded(
                child: MobilePullToRefresh(
                  onRefresh: _handleRefresh,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredMarkers.length,
                    itemBuilder: (context, index) {
                      final marker = filteredMarkers[index];
                      final isSelected = _selectedItems.contains(marker.id);

                      return SwipeableMarkerCard(
                        onEdit: () => _handleMarkerEdit(marker),
                        onDelete: () => _handleMarkerDelete(marker),
                        onShare: () => _handleMarkerShare(marker),
                        child: MarkerListItem(
                          marker: marker,
                          isSelected: isSelected,
                          isSelectionMode: _isSelectionMode,
                          onTap: () => _onMarkerTap(marker),
                          onLongPress: () => _onMarkerLongPress(marker),
                          onSelectionChanged: (selected) => _onSelectionChanged(marker.id!, selected ?? false),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: _isSelectionMode ? null : FloatingActionButton(
        onPressed: () => _addNewMarker(),
        child: const Icon(Icons.add),
      ),
      bottomNavigationBar: _isSelectionMode ? _buildSelectionBottomBar() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    if (_isSelectionMode) {
      return AppBar(
        title: Text('已选择 ${_selectedItems.length} 项'),
        leading: IconButton(
          onPressed: _exitSelectionMode,
          icon: const Icon(Icons.close),
        ),
        actions: [
          IconButton(
            onPressed: _selectedItems.length == context.read<MapProvider>().markers.length
                ? _deselectAll
                : _selectAll,
            icon: Icon(_selectedItems.length == context.read<MapProvider>().markers.length
                ? Icons.deselect
                : Icons.select_all),
          ),
        ],
      );
    }

    return AppBar(
      title: const Text('我的收藏'),
      actions: [
        IconButton(
          onPressed: _showSortFilterSheet,
          icon: const Icon(Icons.sort),
          tooltip: '排序和筛选',
        ),
        PopupMenuButton<String>(
          onSelected: _onMenuSelected,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('导出数据'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('导入数据'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'stats',
              child: ListTile(
                leading: Icon(Icons.analytics),
                title: Text('统计信息'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索标题、描述、标签...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: _clearSearch,
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildStatsBar(int filteredCount, int totalCount) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Text(
            '显示 $filteredCount 个，共 $totalCount 个标记',
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
          ),
          const Spacer(),
          if (_filterOption != FilterOption.all || _searchQuery.isNotEmpty)
            TextButton.icon(
              onPressed: _clearFilters,
              icon: const Icon(Icons.clear_all, size: 16),
              label: const Text('清除筛选'),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _filterOption != FilterOption.all
                ? '没有找到匹配的标记'
                : '还没有收藏任何地点',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _filterOption != FilterOption.all
                ? '尝试调整搜索条件或筛选器'
                : '点击右下角的 + 按钮开始添加',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
          if (_searchQuery.isEmpty && _filterOption == FilterOption.all) ...[
            const SizedBox(height: 24),
            FilledButton.icon(
              onPressed: _addNewMarker,
              icon: const Icon(Icons.add),
              label: const Text('添加第一个标记'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSelectionBottomBar() {
    return BottomAppBar(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: _selectedItems.isNotEmpty ? _deleteSelected : null,
            icon: const Icon(Icons.delete),
            tooltip: '删除',
          ),
          IconButton(
            onPressed: _selectedItems.isNotEmpty ? _shareSelected : null,
            icon: const Icon(Icons.share),
            tooltip: '分享',
          ),
          IconButton(
            onPressed: _selectedItems.isNotEmpty ? _exportSelected : null,
            icon: const Icon(Icons.download),
            tooltip: '导出',
          ),
          IconButton(
            onPressed: _selectedItems.isNotEmpty ? _togglePrivacySelected : null,
            icon: const Icon(Icons.visibility),
            tooltip: '切换隐私',
          ),
        ],
      ),
    );
  }

  List<MarkerModel> _getFilteredAndSortedMarkers(List<MarkerModel> markers) {
    var filtered = markers.where((marker) {
      // 搜索筛选
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesTitle = marker.title.toLowerCase().contains(query);
        final matchesDescription = marker.description?.toLowerCase().contains(query) ?? false;
        final matchesTags = marker.tags.any((tag) => tag.toLowerCase().contains(query));
        
        if (!matchesTitle && !matchesDescription && !matchesTags) {
          return false;
        }
      }

      // 其他筛选
      switch (_filterOption) {
        case FilterOption.public:
          return marker.isPublic;
        case FilterOption.private:
          return !marker.isPublic;
        case FilterOption.withImages:
          return marker.imagePaths.isNotEmpty;
        case FilterOption.withRating:
          return marker.rating != null;
        case FilterOption.all:
        default:
          return true;
      }
    }).toList();

    // 排序
    filtered.sort((a, b) {
      int comparison = 0;
      
      switch (_sortOption) {
        case SortOption.dateCreated:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case SortOption.dateUpdated:
          final aUpdated = a.updatedAt ?? a.createdAt;
          final bUpdated = b.updatedAt ?? b.createdAt;
          comparison = aUpdated.compareTo(bUpdated);
          break;
        case SortOption.title:
          comparison = a.title.compareTo(b.title);
          break;
        case SortOption.category:
          comparison = a.category.compareTo(b.category);
          break;
        case SortOption.rating:
          final aRating = a.rating ?? 0;
          final bRating = b.rating ?? 0;
          comparison = aRating.compareTo(bRating);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _onMarkerTap(MarkerModel marker) {
    if (_isSelectionMode) {
      _onSelectionChanged(marker.id!, !_selectedItems.contains(marker.id));
    } else {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MarkerDetailScreen(marker: marker),
        ),
      );
    }
  }

  void _onMarkerLongPress(MarkerModel marker) {
    if (!_isSelectionMode) {
      setState(() {
        _isSelectionMode = true;
        _selectedItems.add(marker.id!);
      });
    }
  }

  void _onSelectionChanged(int markerId, bool selected) {
    setState(() {
      if (selected) {
        _selectedItems.add(markerId);
      } else {
        _selectedItems.remove(markerId);
      }

      if (_selectedItems.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  Future<void> _handleRefresh() async {
    // 震动反馈
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    // 重新加载数据
    if (mounted) {
      final mapProvider = Provider.of<MapProvider>(context, listen: false);
      await mapProvider.loadMarkers();
    }
  }

  void _handleMarkerEdit(MarkerModel marker) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditMarkerScreen(marker: marker),
      ),
    );
  }

  void _handleMarkerDelete(MarkerModel marker) async {
    final mapProvider = Provider.of<MapProvider>(context, listen: false);
    await mapProvider.deleteMarker(marker.id!);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已删除 "${marker.title}"'),
          action: SnackBarAction(
            label: '撤销',
            onPressed: () {
              mapProvider.addMarker(marker);
            },
          ),
        ),
      );
    }
  }

  void _handleMarkerShare(MarkerModel marker) async {
    // 震动反馈
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    // 显示移动端分享面板
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MobileShareSheet(
        marker: marker,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _showMarkerDetail(MarkerModel marker) {
    showDialog(
      context: context,
      builder: (context) => MarkerDetailDialog(
        marker: marker,
        onEdit: () => _handleMarkerEdit(marker),
        onDelete: () => _handleMarkerDelete(marker),
        onShare: () => _handleMarkerShare(marker),
      ),
    );
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedItems.clear();
    });
  }

  void _selectAll() {
    final mapProvider = context.read<MapProvider>();
    setState(() {
      _selectedItems.addAll(
        mapProvider.markers.where((m) => m.id != null).map((m) => m.id!),
      );
    });
  }

  void _deselectAll() {
    setState(() {
      _selectedItems.clear();
    });
  }

  void _addNewMarker() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEditMarkerScreen(),
      ),
    );
  }

  void _showSortFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => SortFilterBottomSheet(
        sortOption: _sortOption,
        sortAscending: _sortAscending,
        filterOption: _filterOption,
        onSortChanged: (option, ascending) {
          setState(() {
            _sortOption = option;
            _sortAscending = ascending;
          });
        },
        onFilterChanged: (option) {
          setState(() {
            _filterOption = option;
          });
        },
      ),
    );
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  void _clearFilters() {
    setState(() {
      _filterOption = FilterOption.all;
      _searchQuery = '';
    });
    _searchController.clear();
  }

  void _onMenuSelected(String value) {
    switch (value) {
      case 'export':
        _exportData();
        break;
      case 'import':
        _importData();
        break;
      case 'stats':
        _showStats();
        break;
    }
  }

  void _deleteSelected() {
    // TODO: 实现批量删除
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('批量删除功能即将实现')),
    );
  }

  void _shareSelected() {
    // TODO: 实现批量分享
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('批量分享功能即将实现')),
    );
  }

  void _exportSelected() {
    // TODO: 实现批量导出
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('批量导出功能即将实现')),
    );
  }

  void _togglePrivacySelected() {
    // TODO: 实现批量隐私切换
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('批量隐私切换功能即将实现')),
    );
  }

  void _exportData() {
    // TODO: 实现数据导出
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('数据导出功能即将实现')),
    );
  }

  void _importData() {
    // TODO: 实现数据导入
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('数据导入功能即将实现')),
    );
  }

  void _showStats() {
    // TODO: 实现统计信息
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('统计信息功能即将实现')),
    );
  }
}
