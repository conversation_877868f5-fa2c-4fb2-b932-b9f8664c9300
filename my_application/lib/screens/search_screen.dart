import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:heroicons/heroicons.dart';

import '../models/marker_model.dart';
import '../providers/map_provider.dart';
import '../services/search_service.dart';
import '../services/mobile_features_service.dart';
import '../constants/app_colors.dart';
import '../widgets/marker_list_item.dart';
import '../widgets/search_filter_chip.dart';
import '../widgets/dialogs/advanced_filter_dialog.dart';
import '../widgets/enhanced_search_bar.dart';
import 'marker_detail_screen.dart';

class SearchScreen extends StatefulWidget {
  final String? initialQuery;

  const SearchScreen({
    super.key,
    this.initialQuery,
  });

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final SearchService _searchService = SearchService();
  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();

  List<MarkerModel> _searchResults = [];
  List<String> _searchSuggestions = [];
  List<String> _searchHistory = [];
  bool _isLoading = false;
  bool _showSuggestions = true;
  String _currentQuery = '';

  // 高级筛选选项
  AdvancedFilterOptions _filterOptions = const AdvancedFilterOptions();

  // 筛选条件 (保持向后兼容)
  String? _selectedCategory;
  List<String> _selectedTags = [];
  int? _minRating;
  bool? _isPublic;

  @override
  void initState() {
    super.initState();
    _initializeSearch();
    _mobileFeatures.initialize();

    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
      _performSearch(widget.initialQuery!);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _initializeSearch() async {
    await _searchService.initialize();
    _loadSearchHistory();
    _loadSearchSuggestions('');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _buildSearchField(),
        actions: [
          if (_currentQuery.isNotEmpty)
            IconButton(
              onPressed: _showFilterDialog,
              icon: const Icon(Icons.filter_list),
              tooltip: '筛选',
            ),
        ],
      ),
      body: Column(
        children: [
          // 筛选条件显示
          if (_hasActiveFilters()) _buildActiveFilters(),
          
          // 搜索结果或建议
          Expanded(
            child: _showSuggestions ? _buildSuggestions() : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return EnhancedSearchBar(
      controller: _searchController,
      focusNode: _searchFocusNode,
      hintText: '搜索地点、标签...',
      hasActiveFilters: _filterOptions.hasActiveFilters,
      onFilterTap: _showFilterDialog,
      onChanged: _onSearchChanged,
      onSubmitted: _onSearchSubmitted,
      onClear: _clearSearch,
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          if (_selectedCategory != null)
            SearchFilterChip(
              label: '分类: $_selectedCategory',
              onDeleted: () => setState(() => _selectedCategory = null),
            ),
          if (_selectedTags.isNotEmpty)
            ...(_selectedTags.map((tag) => SearchFilterChip(
              label: '标签: #$tag',
              onDeleted: () => setState(() => _selectedTags.remove(tag)),
            ))),
          if (_minRating != null)
            SearchFilterChip(
              label: '评分: ${_minRating}星以上',
              onDeleted: () => setState(() => _minRating = null),
            ),
          if (_isPublic != null)
            SearchFilterChip(
              label: _isPublic! ? '公开' : '私密',
              onDeleted: () => setState(() => _isPublic = null),
            ),
        ],
      ),
    );
  }

  Widget _buildSuggestions() {
    return ListView(
      children: [
        // 搜索历史
        if (_searchHistory.isNotEmpty) ...[
          _buildSectionHeader('搜索历史', onClear: _clearSearchHistory),
          ..._searchHistory.map((item) => _buildSuggestionItem(
            item,
            Icons.history,
            onTap: () => _selectSuggestion(item),
            onDelete: () => _removeHistoryItem(item),
          )),
        ],
        
        // 搜索建议
        if (_searchSuggestions.isNotEmpty) ...[
          _buildSectionHeader('搜索建议'),
          ..._searchSuggestions.map((item) => _buildSuggestionItem(
            item,
            Icons.search,
            onTap: () => _selectSuggestion(item),
          )),
        ],
        
        // 热门搜索
        FutureBuilder<List<String>>(
          future: _searchService.getPopularSearchTerms(),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data!.isNotEmpty) {
              return Column(
                children: [
                  _buildSectionHeader('热门搜索'),
                  ...snapshot.data!.map((item) => _buildSuggestionItem(
                    item,
                    Icons.trending_up,
                    onTap: () => _selectSuggestion(item),
                  )),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isEmpty) {
      return _buildEmptyResults();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final marker = _searchResults[index];
        return MarkerListItem(
          marker: marker,
          isSelected: false,
          isSelectionMode: false,
          onTap: () => _openMarkerDetail(marker),
          onLongPress: () {},
          onSelectionChanged: (_) {},
        );
      },
    );
  }

  Widget _buildEmptyResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '没有找到相关结果',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '尝试使用不同的关键词或调整筛选条件',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, {VoidCallback? onClear}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          if (onClear != null)
            TextButton(
              onPressed: onClear,
              child: const Text('清除'),
            ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(
    String text,
    IconData icon, {
    required VoidCallback onTap,
    VoidCallback? onDelete,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.textSecondary),
      title: Text(text),
      trailing: onDelete != null
          ? IconButton(
              onPressed: onDelete,
              icon: const Icon(Icons.close, size: 20),
            )
          : null,
      onTap: onTap,
    );
  }

  void _onSearchChanged(String value) {
    setState(() {
      _currentQuery = value;
      _showSuggestions = value.isEmpty;
    });
    
    if (value.isNotEmpty) {
      _loadSearchSuggestions(value);
    }
  }

  void _onSearchSubmitted(String value) {
    if (value.trim().isNotEmpty) {
      _performSearch(value.trim());
    }
  }

  void _selectSuggestion(String suggestion) async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    _searchController.text = suggestion;
    _performSearch(suggestion);
  }

  Future<void> _performSearch(String query) async {
    setState(() {
      _isLoading = true;
      _showSuggestions = false;
      _currentQuery = query;
    });

    try {
      final results = await _searchService.advancedSearch(
        query: query,
        category: _selectedCategory,
        tags: _selectedTags.isNotEmpty ? _selectedTags : null,
        minRating: _minRating,
        isPublic: _isPublic,
      );

      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜索失败: $e')),
        );
      }
    }
  }

  void _loadSearchHistory() {
    setState(() {
      _searchHistory = _searchService.searchHistory;
    });
  }

  void _loadSearchSuggestions(String query) {
    setState(() {
      _searchSuggestions = _searchService.getSearchSuggestions(query);
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _currentQuery = '';
      _showSuggestions = true;
      _searchResults = [];
    });
    _loadSearchSuggestions('');
  }

  Future<void> _clearSearchHistory() async {
    await _searchService.clearSearchHistory();
    _loadSearchHistory();
  }

  Future<void> _removeHistoryItem(String item) async {
    await _searchService.removeSearchHistoryItem(item);
    _loadSearchHistory();
  }

  void _openMarkerDetail(MarkerModel marker) async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    if (!mounted) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MarkerDetailScreen(marker: marker),
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedCategory != null ||
           _selectedTags.isNotEmpty ||
           _minRating != null ||
           _isPublic != null;
  }

  void _showFilterDialog() async {
    await _mobileFeatures.combinedFeedback(
      vibrationPattern: VibrationPattern.light,
    );

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AdvancedFilterDialog(
        selectedCategory: _filterOptions.category,
        selectedTags: _filterOptions.tags,
        minRating: _filterOptions.minRating,
        isPublic: _filterOptions.isPublic,
        startDate: _filterOptions.startDate,
        endDate: _filterOptions.endDate,
        onApply: (filterOptions) {
          setState(() {
            _filterOptions = filterOptions;
            // 同步到旧的筛选变量以保持兼容性
            _selectedCategory = filterOptions.category;
            _selectedTags = List.from(filterOptions.tags);
            _minRating = filterOptions.minRating?.toInt();
            _isPublic = filterOptions.isPublic;
          });
          _performAdvancedSearch();
        },
      ),
    );
  }

  void _performAdvancedSearch() async {
    if (_currentQuery.isEmpty && !_filterOptions.hasActiveFilters) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final results = await _searchService.searchWithAdvancedFilters(
        query: _currentQuery,
        filterOptions: _filterOptions,
      );

      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜索失败: $e')),
        );
      }
    }
  }
}
