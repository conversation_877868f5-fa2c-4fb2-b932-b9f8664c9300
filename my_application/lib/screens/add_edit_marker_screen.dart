import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';

import '../models/marker_model.dart';
import '../providers/map_provider.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/category_selector.dart';
import '../widgets/image_picker_widget.dart';
import '../widgets/tags_input_widget.dart';

class AddEditMarkerScreen extends StatefulWidget {
  final MarkerModel? marker;
  final LatLng? initialLocation;

  const AddEditMarkerScreen({
    super.key,
    this.marker,
    this.initialLocation,
  });

  @override
  State<AddEditMarkerScreen> createState() => _AddEditMarkerScreenState();
}

class _AddEditMarkerScreenState extends State<AddEditMarkerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  
  String _selectedCategory = AppConstants.defaultCategories.first;
  List<String> _imagePaths = [];
  List<String> _tags = [];
  int? _rating;
  bool _isPublic = false;
  bool _isLoading = false;
  LatLng? _location;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    if (widget.marker != null) {
      // 编辑模式
      final marker = widget.marker!;
      _titleController.text = marker.title;
      _descriptionController.text = marker.description ?? '';
      _addressController.text = marker.address ?? '';
      _selectedCategory = marker.category;
      _imagePaths = List.from(marker.imagePaths);
      _tags = List.from(marker.tags);
      _rating = marker.rating;
      _isPublic = marker.isPublic;
      _location = LatLng(marker.latitude, marker.longitude);
    } else if (widget.initialLocation != null) {
      // 新增模式，有初始位置
      _location = widget.initialLocation;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.marker != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? '编辑标记' : '添加标记'),
        actions: [
          if (isEditing)
            IconButton(
              onPressed: _deleteMarker,
              icon: const Icon(Icons.delete),
              tooltip: '删除标记',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题输入
                    _buildTitleField(),
                    const SizedBox(height: 16),
                    
                    // 分类选择
                    _buildCategorySelector(),
                    const SizedBox(height: 16),
                    
                    // 描述输入
                    _buildDescriptionField(),
                    const SizedBox(height: 16),
                    
                    // 地址输入
                    _buildAddressField(),
                    const SizedBox(height: 16),
                    
                    // 评分
                    _buildRatingSelector(),
                    const SizedBox(height: 16),
                    
                    // 图片选择
                    _buildImagePicker(),
                    const SizedBox(height: 16),
                    
                    // 标签输入
                    _buildTagsInput(),
                    const SizedBox(height: 16),
                    
                    // 位置选择
                    _buildLocationSelector(),
                    const SizedBox(height: 16),
                    
                    // 隐私设置
                    _buildPrivacySettings(),
                    const SizedBox(height: 32),
                    
                    // 保存按钮
                    _buildSaveButton(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: '标题 *',
        hintText: '输入地点名称',
        prefixIcon: Icon(Icons.title),
      ),
      maxLength: AppConstants.maxTitleLength,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '请输入标题';
        }
        return null;
      },
    );
  }

  Widget _buildCategorySelector() {
    return Consumer<MapProvider>(
      builder: (context, mapProvider, child) {
        return CategorySelector(
          categories: mapProvider.categories,
          selectedCategory: _selectedCategory,
          onCategorySelected: (category) {
            setState(() {
              _selectedCategory = category;
            });
          },
        );
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: '描述',
        hintText: '添加详细描述...',
        prefixIcon: Icon(Icons.description),
      ),
      maxLines: 3,
      maxLength: AppConstants.maxDescriptionLength,
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      decoration: const InputDecoration(
        labelText: '地址',
        hintText: '输入详细地址',
        prefixIcon: Icon(Icons.location_on),
      ),
    );
  }

  Widget _buildRatingSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '评分',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(5, (index) {
            return IconButton(
              onPressed: () {
                setState(() {
                  _rating = _rating == index + 1 ? null : index + 1;
                });
              },
              icon: Icon(
                index < (_rating ?? 0) ? Icons.star : Icons.star_border,
                color: Colors.amber,
                size: 32,
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildImagePicker() {
    return ImagePickerWidget(
      imagePaths: _imagePaths,
      onImagesChanged: (paths) {
        setState(() {
          _imagePaths = paths;
        });
      },
    );
  }

  Widget _buildTagsInput() {
    return TagsInputWidget(
      tags: _tags,
      onTagsChanged: (tags) {
        setState(() {
          _tags = tags;
        });
      },
    );
  }

  Widget _buildLocationSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '位置',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            if (_location != null)
              Text(
                '纬度: ${_location!.latitude.toStringAsFixed(6)}\n'
                '经度: ${_location!.longitude.toStringAsFixed(6)}',
                style: const TextStyle(color: AppColors.textSecondary),
              )
            else
              const Text(
                '未选择位置',
                style: TextStyle(color: AppColors.textSecondary),
              ),
            const SizedBox(height: 8),
            OutlinedButton.icon(
              onPressed: _selectLocation,
              icon: const Icon(Icons.map),
              label: const Text('选择位置'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '隐私设置',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            SwitchListTile(
              title: const Text('公开分享'),
              subtitle: const Text('允许其他人查看此标记'),
              value: _isPublic,
              onChanged: (value) {
                setState(() {
                  _isPublic = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: _saveMarker,
        icon: const Icon(Icons.save),
        label: Text(widget.marker != null ? '更新标记' : '保存标记'),
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  void _selectLocation() {
    // TODO: 实现位置选择功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('位置选择功能即将实现')),
    );
  }

  Future<void> _saveMarker() async {
    if (!_formKey.currentState!.validate()) return;
    if (_location == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择位置')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final mapProvider = context.read<MapProvider>();
      
      final marker = MarkerModel(
        id: widget.marker?.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        latitude: _location!.latitude,
        longitude: _location!.longitude,
        category: _selectedCategory,
        imagePaths: _imagePaths,
        tags: _tags,
        createdAt: widget.marker?.createdAt ?? DateTime.now(),
        updatedAt: widget.marker != null ? DateTime.now() : null,
        isPublic: _isPublic,
        address: _addressController.text.trim().isEmpty 
            ? null 
            : _addressController.text.trim(),
        rating: _rating,
        shareId: widget.marker?.shareId,
      );

      if (widget.marker != null) {
        await mapProvider.updateMarker(marker);
      } else {
        await mapProvider.addMarker(marker);
      }

      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.marker != null ? '标记已更新' : '标记已保存'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteMarker() async {
    if (widget.marker?.id == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除标记'),
        content: const Text('确定要删除这个标记吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final mapProvider = context.read<MapProvider>();
        await mapProvider.deleteMarker(widget.marker!.id!);
        
        if (mounted) {
          Navigator.of(context).pop(true);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('标记已删除')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('删除失败: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
