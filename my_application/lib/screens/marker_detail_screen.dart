import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import '../models/marker_model.dart';
import '../providers/map_provider.dart';
import '../constants/app_colors.dart';
import '../widgets/image_gallery_widget.dart';
import '../widgets/share_dialog.dart';
import 'add_edit_marker_screen.dart';

class MarkerDetailScreen extends StatefulWidget {
  final MarkerModel marker;

  const MarkerDetailScreen({
    super.key,
    required this.marker,
  });

  @override
  State<MarkerDetailScreen> createState() => _MarkerDetailScreenState();
}

class _MarkerDetailScreenState extends State<MarkerDetailScreen> {
  late MarkerModel _marker;

  @override
  void initState() {
    super.initState();
    _marker = widget.marker;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // 应用栏
          _buildSliverAppBar(),
          
          // 内容
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和分类
                  _buildTitleSection(),
                  const SizedBox(height: 16),
                  
                  // 评分
                  if (_marker.rating != null) ...[
                    _buildRatingSection(),
                    const SizedBox(height: 16),
                  ],
                  
                  // 描述
                  if (_marker.description != null && _marker.description!.isNotEmpty) ...[
                    _buildDescriptionSection(),
                    const SizedBox(height: 16),
                  ],
                  
                  // 地址
                  if (_marker.address != null && _marker.address!.isNotEmpty) ...[
                    _buildAddressSection(),
                    const SizedBox(height: 16),
                  ],
                  
                  // 标签
                  if (_marker.tags.isNotEmpty) ...[
                    _buildTagsSection(),
                    const SizedBox(height: 16),
                  ],
                  
                  // 位置信息
                  _buildLocationSection(),
                  const SizedBox(height: 16),
                  
                  // 时间信息
                  _buildTimeSection(),
                  const SizedBox(height: 16),
                  
                  // 隐私状态
                  _buildPrivacySection(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
      
      // 底部操作栏
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: _marker.imagePaths.isNotEmpty ? 300 : 120,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _marker.title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 3,
                color: Colors.black54,
              ),
            ],
          ),
        ),
        background: _marker.imagePaths.isNotEmpty
            ? ImageGalleryWidget(
                imagePaths: _marker.imagePaths,
                heroTag: 'marker_${_marker.id}',
              )
            : Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.getCategoryColor(_marker.category).withOpacity(0.8),
                      AppColors.getCategoryColor(_marker.category),
                    ],
                  ),
                ),
                child: Center(
                  child: Icon(
                    _getCategoryIcon(_marker.category),
                    size: 64,
                    color: Colors.white,
                  ),
                ),
              ),
      ),
      actions: [
        IconButton(
          onPressed: _shareMarker,
          icon: const Icon(Icons.share),
          tooltip: '分享',
        ),
        PopupMenuButton<String>(
          onSelected: _onMenuSelected,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('编辑'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete),
                title: Text('删除'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            PopupMenuItem(
              value: 'privacy',
              child: ListTile(
                leading: Icon(_marker.isPublic ? Icons.lock : Icons.public),
                title: Text(_marker.isPublic ? '设为私密' : '设为公开'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTitleSection() {
    return Row(
      children: [
        Expanded(
          child: Text(
            _marker.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppColors.getCategoryColor(_marker.category).withOpacity(0.2),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getCategoryIcon(_marker.category),
                size: 16,
                color: AppColors.getCategoryColor(_marker.category),
              ),
              const SizedBox(width: 4),
              Text(
                _marker.category,
                style: TextStyle(
                  color: AppColors.getCategoryColor(_marker.category),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Row(
      children: [
        const Text(
          '评分：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Row(
          children: List.generate(5, (index) {
            return Icon(
              index < _marker.rating! ? Icons.star : Icons.star_border,
              color: Colors.amber,
              size: 24,
            );
          }),
        ),
        const SizedBox(width: 8),
        Text(
          '${_marker.rating}/5',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _marker.description!,
          style: const TextStyle(
            fontSize: 16,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '地址',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(
              Icons.location_on,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _marker.address!,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标签',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _marker.tags.map((tag) {
            return Chip(
              label: Text('#$tag'),
              backgroundColor: AppColors.primary.withOpacity(0.1),
              labelStyle: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
              side: const BorderSide(color: AppColors.primary),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '位置坐标',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const Text('纬度：', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text(_marker.latitude.toStringAsFixed(6)),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Text('经度：', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text(_marker.longitude.toStringAsFixed(6)),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '时间信息',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const Text('创建时间：', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text(DateFormat('yyyy-MM-dd HH:mm').format(_marker.createdAt)),
                ],
              ),
              if (_marker.updatedAt != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Text('更新时间：', style: TextStyle(fontWeight: FontWeight.w500)),
                    Text(DateFormat('yyyy-MM-dd HH:mm').format(_marker.updatedAt!)),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacySection() {
    return Row(
      children: [
        Icon(
          _marker.isPublic ? Icons.public : Icons.lock,
          color: _marker.isPublic ? AppColors.success : AppColors.warning,
        ),
        const SizedBox(width: 8),
        Text(
          _marker.isPublic ? '公开分享' : '私密收藏',
          style: TextStyle(
            color: _marker.isPublic ? AppColors.success : AppColors.warning,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    return BottomAppBar(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: _editMarker,
            icon: const Icon(Icons.edit),
            tooltip: '编辑',
          ),
          IconButton(
            onPressed: _shareMarker,
            icon: const Icon(Icons.share),
            tooltip: '分享',
          ),
          IconButton(
            onPressed: _showOnMap,
            icon: const Icon(Icons.map),
            tooltip: '在地图上查看',
          ),
          IconButton(
            onPressed: _deleteMarker,
            icon: const Icon(Icons.delete),
            tooltip: '删除',
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '餐厅':
        return Icons.restaurant;
      case '咖啡馆':
        return Icons.local_cafe;
      case '书店':
        return Icons.menu_book;
      case '景点':
        return Icons.place;
      case '购物':
        return Icons.shopping_bag;
      case '工作':
        return Icons.work;
      case '旅行':
        return Icons.flight;
      default:
        return Icons.place;
    }
  }

  void _onMenuSelected(String value) {
    switch (value) {
      case 'edit':
        _editMarker();
        break;
      case 'delete':
        _deleteMarker();
        break;
      case 'privacy':
        _togglePrivacy();
        break;
    }
  }

  void _editMarker() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditMarkerScreen(marker: _marker),
      ),
    ).then((result) {
      if (result == true) {
        // 重新加载标记数据
        _reloadMarker();
      }
    });
  }

  void _shareMarker() {
    showDialog(
      context: context,
      builder: (context) => ShareDialog(marker: _marker),
    );
  }

  void _showOnMap() {
    Navigator.of(context).popUntil((route) => route.isFirst);
    // 在地图上显示此标记
    context.read<MapProvider>().moveToMarker(_marker);
  }

  Future<void> _deleteMarker() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除标记'),
        content: Text('确定要删除"${_marker.title}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true && _marker.id != null) {
      try {
        await context.read<MapProvider>().deleteMarker(_marker.id!);
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('标记已删除')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('删除失败: $e')),
          );
        }
      }
    }
  }

  Future<void> _togglePrivacy() async {
    try {
      final updatedMarker = _marker.copyWith(isPublic: !_marker.isPublic);
      await context.read<MapProvider>().updateMarker(updatedMarker);
      setState(() {
        _marker = updatedMarker;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_marker.isPublic ? '已设为公开' : '已设为私密'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('更新失败: $e')),
        );
      }
    }
  }

  Future<void> _reloadMarker() async {
    if (_marker.id != null) {
      final mapProvider = context.read<MapProvider>();
      final updatedMarker = mapProvider.markers.firstWhere(
        (m) => m.id == _marker.id,
        orElse: () => _marker,
      );
      setState(() {
        _marker = updatedMarker;
      });
    }
  }
}
