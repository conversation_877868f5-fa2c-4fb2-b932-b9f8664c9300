import 'package:flutter/material.dart';

class AppColors {
  // 主色调 - 橘红色系
  static const Color primary = Color(0xFFFF5722); // 橘红色
  static const Color primaryDark = Color(0xFFE64A19); // 深橘红色
  static const Color primaryLight = Color(0xFFFF8A65); // 浅橘红色

  // 辅助色 - 橘红色渐变
  static const Color secondary = Color(0xFFFF7043); // 中等橘红色
  static const Color secondaryDark = Color(0xFFD84315); // 深橘色
  static const Color secondaryLight = Color(0xFFFFAB91); // 浅橘色

  // 背景色
  static const Color background = Color(0xFFFFFFFF);
  static const Color backgroundDark = Color(0xFF000000);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF121212);

  // 文字色
  static const Color textPrimary = Color(0xFF424242); // 深灰色文字
  static const Color textSecondary = Color(0xFF757575); // 中灰色文字
  static const Color textOnPrimary = Color(0xFFFFFFFF); // 白色文字（在橘红色背景上）
  static const Color textOnSecondary = Color(0xFFFFFFFF); // 白色文字（在橘色背景上）
  
  // 状态色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // 分类颜色 - 橘红色系
  static const Color categoryRestaurant = Color(0xFFFF5722); // 橘红色
  static const Color categoryCafe = Color(0xFFFF7043); // 中橘红色
  static const Color categoryBookstore = Color(0xFFFF8A65); // 浅橘红色
  static const Color categoryAttraction = Color(0xFFFFAB91); // 很浅橘红色
  static const Color categoryShopping = Color(0xFFE64A19); // 深橘红色
  static const Color categoryWork = Color(0xFFD84315); // 更深橘红色
  static const Color categoryTravel = Color(0xFFBF360C); // 最深橘红色
  static const Color categoryOther = Color(0xFFFFCCBC); // 极浅橘红色
  
  // 透明度变体
  static Color primaryWithOpacity(double opacity) => primary.withOpacity(opacity);
  static Color backgroundWithOpacity(double opacity) => background.withOpacity(opacity);

  // 新的透明度方法（使用withValues）
  static Color primaryWithAlpha(double alpha) => primary.withValues(alpha: alpha);
  static Color secondaryWithAlpha(double alpha) => secondary.withValues(alpha: alpha);

  // 常用透明度预设
  static Color get primaryLight10 => primary.withValues(alpha: 0.1);
  static Color get primaryLight20 => primary.withValues(alpha: 0.2);
  static Color get primaryLight30 => primary.withValues(alpha: 0.3);
  static Color get primaryLight50 => primary.withValues(alpha: 0.5);
  static Color get primaryLight70 => primary.withValues(alpha: 0.7);
  static Color get primaryLight80 => primary.withValues(alpha: 0.8);
  
  // 获取分类颜色
  static Color getCategoryColor(String category) {
    switch (category) {
      case '餐厅':
        return categoryRestaurant;
      case '咖啡馆':
        return categoryCafe;
      case '书店':
        return categoryBookstore;
      case '景点':
        return categoryAttraction;
      case '购物':
        return categoryShopping;
      case '工作':
        return categoryWork;
      case '旅行':
        return categoryTravel;
      default:
        return categoryOther;
    }
  }
}
