/// 高德地图配置文件
class AmapConfig {
  // 高德地图 API Key
  static const String androidApiKey = '7e2b9a68a29a502de59bca3affb3c68d';
  static const String iosApiKey = '7e2b9a68a29a502de59bca3affb3c68d';
  
  // 地图类型
  static const int mapTypeNormal = 1; // 标准地图
  static const int mapTypeSatellite = 2; // 卫星地图
  static const int mapTypeNight = 3; // 夜间地图
  static const int mapTypeNavigation = 4; // 导航地图
  static const int mapTypeBus = 5; // 公交地图
  
  // 默认地图设置
  static const double defaultZoom = 16.0;
  static const double minZoom = 3.0;
  static const double maxZoom = 20.0;
  
  // 默认位置（北京天安门）
  static const double defaultLatitude = 39.9042;
  static const double defaultLongitude = 116.4074;
  
  // 定位相关配置
  static const int locationInterval = 2000; // 定位间隔（毫秒）
  static const bool needAddress = true; // 是否需要地址信息
  static const bool onceLocation = false; // 是否单次定位
  
  // 地图UI配置
  static const bool showZoomControls = true; // 显示缩放控件
  static const bool showCompass = true; // 显示指南针
  static const bool showScale = true; // 显示比例尺
  static const bool showMyLocationButton = true; // 显示定位按钮
  static const bool gestureScaleByMapCenter = false; // 手势缩放是否以地图中心点为锚点
  
  // 地图手势配置
  static const bool zoomGesturesEnabled = true; // 缩放手势
  static const bool scrollGesturesEnabled = true; // 滑动手势
  static const bool rotateGesturesEnabled = true; // 旋转手势
  static const bool tiltGesturesEnabled = true; // 倾斜手势
  
  // 地图样式
  static const String customMapStyle = '''
  [
    {
      "featureType": "water",
      "elementType": "all",
      "stylers": {
        "color": "#d1d1d1"
      }
    },
    {
      "featureType": "land",
      "elementType": "all",
      "stylers": {
        "color": "#f3f3f3"
      }
    }
  ]
  ''';
}
