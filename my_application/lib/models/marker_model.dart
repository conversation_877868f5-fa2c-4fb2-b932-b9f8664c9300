import 'dart:convert';

class MarkerModel {
  final int? id;
  final String title;
  final String? description;
  final double latitude;
  final double longitude;
  final String category;
  final List<String> imagePaths;
  final String? audioPath;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isPublic;
  final String? shareId;
  final String? address;
  final int? rating; // 1-5星评分

  MarkerModel({
    this.id,
    required this.title,
    this.description,
    required this.latitude,
    required this.longitude,
    required this.category,
    this.imagePaths = const [],
    this.audioPath,
    this.tags = const [],
    required this.createdAt,
    this.updatedAt,
    this.isPublic = false,
    this.shareId,
    this.address,
    this.rating,
  });

  // 从数据库Map创建对象
  factory MarkerModel.fromMap(Map<String, dynamic> map) {
    return MarkerModel(
      id: map['_id'],
      title: map['title'],
      description: map['description'],
      latitude: map['latitude'],
      longitude: map['longitude'],
      category: map['category'],
      imagePaths: map['image_paths'] != null 
          ? List<String>.from(jsonDecode(map['image_paths']))
          : [],
      audioPath: map['audio_path'],
      tags: map['tags'] != null 
          ? List<String>.from(jsonDecode(map['tags']))
          : [],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'])
          : null,
      isPublic: map['is_public'] == 1,
      shareId: map['share_id'],
      address: map['address'],
      rating: map['rating'],
    );
  }

  // 转换为数据库Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) '_id': id,
      'title': title,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'category': category,
      'image_paths': jsonEncode(imagePaths),
      'audio_path': audioPath,
      'tags': jsonEncode(tags),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_public': isPublic ? 1 : 0,
      'share_id': shareId,
      'address': address,
      'rating': rating,
    };
  }

  // 复制对象并修改部分属性
  MarkerModel copyWith({
    int? id,
    String? title,
    String? description,
    double? latitude,
    double? longitude,
    String? category,
    List<String>? imagePaths,
    String? audioPath,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPublic,
    String? shareId,
    String? address,
    int? rating,
  }) {
    return MarkerModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      category: category ?? this.category,
      imagePaths: imagePaths ?? this.imagePaths,
      audioPath: audioPath ?? this.audioPath,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPublic: isPublic ?? this.isPublic,
      shareId: shareId ?? this.shareId,
      address: address ?? this.address,
      rating: rating ?? this.rating,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'category': category,
      'imagePaths': imagePaths,
      'audioPath': audioPath,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isPublic': isPublic,
      'shareId': shareId,
      'address': address,
      'rating': rating,
    };
  }

  // 从JSON创建对象
  factory MarkerModel.fromJson(Map<String, dynamic> json) {
    return MarkerModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      category: json['category'],
      imagePaths: List<String>.from(json['imagePaths'] ?? []),
      audioPath: json['audioPath'],
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'])
          : null,
      isPublic: json['isPublic'] ?? false,
      shareId: json['shareId'],
      address: json['address'],
      rating: json['rating'],
    );
  }

  @override
  String toString() {
    return 'MarkerModel{id: $id, title: $title, category: $category, latitude: $latitude, longitude: $longitude}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
