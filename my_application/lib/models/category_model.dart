import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

class CategoryModel {
  final int? id;
  final String name;
  final String icon;
  final Color color;
  final int sortOrder;
  final bool isDefault;
  final DateTime createdAt;

  CategoryModel({
    this.id,
    required this.name,
    required this.icon,
    required this.color,
    this.sortOrder = 0,
    this.isDefault = false,
    required this.createdAt,
  });

  // 从数据库Map创建对象
  factory CategoryModel.fromMap(Map<String, dynamic> map) {
    return CategoryModel(
      id: map['_id'],
      name: map['name'],
      icon: map['icon'],
      color: Color(map['color']),
      sortOrder: map['sort_order'] ?? 0,
      isDefault: map['is_default'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // 转换为数据库Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) '_id': id,
      'name': name,
      'icon': icon,
      'color': color.value,
      'sort_order': sortOrder,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // 复制对象并修改部分属性
  CategoryModel copyWith({
    int? id,
    String? name,
    String? icon,
    Color? color,
    int? sortOrder,
    bool? isDefault,
    DateTime? createdAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      sortOrder: sortOrder ?? this.sortOrder,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // 默认分类列表
  static List<CategoryModel> getDefaultCategories() {
    final now = DateTime.now();
    return [
      CategoryModel(
        name: '餐厅',
        icon: 'restaurant',
        color: AppColors.categoryRestaurant,
        sortOrder: 1,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '咖啡馆',
        icon: 'local_cafe',
        color: AppColors.categoryCafe,
        sortOrder: 2,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '书店',
        icon: 'menu_book',
        color: AppColors.categoryBookstore,
        sortOrder: 3,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '景点',
        icon: 'place',
        color: AppColors.categoryAttraction,
        sortOrder: 4,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '购物',
        icon: 'shopping_bag',
        color: AppColors.categoryShopping,
        sortOrder: 5,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '工作',
        icon: 'work',
        color: AppColors.categoryWork,
        sortOrder: 6,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '旅行',
        icon: 'flight',
        color: AppColors.categoryTravel,
        sortOrder: 7,
        isDefault: true,
        createdAt: now,
      ),
      CategoryModel(
        name: '其他',
        icon: 'more_horiz',
        color: AppColors.categoryOther,
        sortOrder: 8,
        isDefault: true,
        createdAt: now,
      ),
    ];
  }

  @override
  String toString() {
    return 'CategoryModel{id: $id, name: $name, icon: $icon}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
