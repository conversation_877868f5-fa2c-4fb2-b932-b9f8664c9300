import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vibration/vibration.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

/// 移动端特性服务
/// 提供震动反馈、系统分享、PWA功能和原生移动交互
class MobileFeaturesService {
  static final MobileFeaturesService _instance = MobileFeaturesService._internal();
  factory MobileFeaturesService() => _instance;
  MobileFeaturesService._internal();

  bool _isInitialized = false;
  bool _hasVibration = false;
  bool _isAndroid = false;
  bool _isIOS = false;
  DeviceInfoPlugin? _deviceInfo;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否支持震动
  bool get hasVibration => _hasVibration;

  /// 是否为Android平台
  bool get isAndroid => _isAndroid;

  /// 是否为iOS平台
  bool get isIOS => _isIOS;

  /// 初始化移动特性服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _deviceInfo = DeviceInfoPlugin();
      
      // 检测平台
      if (Platform.isAndroid) {
        _isAndroid = true;
      } else if (Platform.isIOS) {
        _isIOS = true;
      }

      // 检查震动支持
      if (!kIsWeb) {
        _hasVibration = await Vibration.hasVibrator() ?? false;
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('移动特性服务初始化完成 - 平台: ${Platform.operatingSystem}, 震动支持: $_hasVibration');
      }
    } catch (e) {
      if (kDebugMode) {
        print('移动特性服务初始化失败: $e');
      }
    }
  }

  /// 震动反馈
  Future<void> vibrate({
    VibrationPattern pattern = VibrationPattern.light,
    int duration = 50,
    List<int>? customPattern,
  }) async {
    if (!_hasVibration || kIsWeb) return;

    try {
      if (customPattern != null) {
        await Vibration.vibrate(pattern: customPattern);
      } else {
        switch (pattern) {
          case VibrationPattern.light:
            await Vibration.vibrate(duration: 30);
            break;
          case VibrationPattern.medium:
            await Vibration.vibrate(duration: 50);
            break;
          case VibrationPattern.heavy:
            await Vibration.vibrate(duration: 100);
            break;
          case VibrationPattern.success:
            await Vibration.vibrate(pattern: [0, 50, 50, 50]);
            break;
          case VibrationPattern.error:
            await Vibration.vibrate(pattern: [0, 100, 50, 100]);
            break;
          case VibrationPattern.warning:
            await Vibration.vibrate(pattern: [0, 50, 100, 50, 100]);
            break;
          case VibrationPattern.custom:
            await Vibration.vibrate(duration: duration);
            break;
        }
      }

      if (kDebugMode) {
        print('震动反馈执行: $pattern');
      }
    } catch (e) {
      if (kDebugMode) {
        print('震动反馈失败: $e');
      }
    }
  }

  /// 系统分享
  Future<bool> shareToSystem({
    required String title,
    String? text,
    String? url,
    List<String>? files,
    String? subject,
  }) async {
    try {
      if (files != null && files.isNotEmpty) {
        // 分享文件
        final xFiles = files.map((path) => XFile(path)).toList();
        await Share.shareXFiles(
          xFiles,
          text: text,
          subject: subject ?? title,
        );
      } else {
        // 分享文本
        final shareText = [
          if (text != null) text,
          if (url != null) url,
        ].join('\n\n');
        
        await Share.share(
          shareText.isNotEmpty ? shareText : title,
          subject: subject ?? title,
        );
      }

      if (kDebugMode) {
        print('系统分享成功: $title');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('系统分享失败: $e');
      }
      return false;
    }
  }

  /// 复制到剪贴板
  Future<bool> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      
      if (kDebugMode) {
        print('复制到剪贴板成功');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('复制到剪贴板失败: $e');
      }
      return false;
    }
  }

  /// 从剪贴板读取
  Future<String?> readFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      return clipboardData?.text;
    } catch (e) {
      if (kDebugMode) {
        print('读取剪贴板失败: $e');
      }
      return null;
    }
  }

  /// 打开外部应用
  Future<bool> openExternalApp({
    required String url,
    LaunchMode mode = LaunchMode.externalApplication,
  }) async {
    try {
      final uri = Uri.parse(url);
      final canLaunch = await canLaunchUrl(uri);
      
      if (canLaunch) {
        await launchUrl(uri, mode: mode);
        return true;
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('打开外部应用失败: $e');
      }
      return false;
    }
  }

  /// 打开邮件应用
  Future<bool> openEmailApp({
    String? email,
    String? subject,
    String? body,
  }) async {
    final emailUrl = StringBuffer('mailto:');
    
    if (email != null) {
      emailUrl.write(email);
    }
    
    final params = <String>[];
    if (subject != null) {
      params.add('subject=${Uri.encodeComponent(subject)}');
    }
    if (body != null) {
      params.add('body=${Uri.encodeComponent(body)}');
    }
    
    if (params.isNotEmpty) {
      emailUrl.write('?${params.join('&')}');
    }
    
    return await openExternalApp(url: emailUrl.toString());
  }

  /// 打开短信应用
  Future<bool> openSMSApp({
    String? phoneNumber,
    String? message,
  }) async {
    final smsUrl = StringBuffer('sms:');
    
    if (phoneNumber != null) {
      smsUrl.write(phoneNumber);
    }
    
    if (message != null) {
      if (_isIOS) {
        smsUrl.write('&body=${Uri.encodeComponent(message)}');
      } else {
        smsUrl.write('?body=${Uri.encodeComponent(message)}');
      }
    }
    
    return await openExternalApp(url: smsUrl.toString());
  }

  /// 打开电话应用
  Future<bool> openPhoneApp(String phoneNumber) async {
    return await openExternalApp(url: 'tel:$phoneNumber');
  }

  /// 打开地图应用
  Future<bool> openMapApp({
    required double latitude,
    required double longitude,
    String? label,
  }) async {
    String mapUrl;
    
    if (_isIOS) {
      // iOS 使用 Apple Maps
      mapUrl = 'http://maps.apple.com/?ll=$latitude,$longitude';
      if (label != null) {
        mapUrl += '&q=${Uri.encodeComponent(label)}';
      }
    } else {
      // Android 使用 Google Maps
      mapUrl = 'geo:$latitude,$longitude';
      if (label != null) {
        mapUrl += '?q=$latitude,$longitude(${Uri.encodeComponent(label)})';
      }
    }
    
    return await openExternalApp(url: mapUrl);
  }

  /// 获取设备信息
  Future<Map<String, dynamic>> getDeviceInfo() async {
    if (_deviceInfo == null) return {};
    
    try {
      if (_isAndroid) {
        final androidInfo = await _deviceInfo!.androidInfo;
        return {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
        };
      } else if (_isIOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        return {
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemVersion': iosInfo.systemVersion,
          'localizedModel': iosInfo.localizedModel,
          'identifierForVendor': iosInfo.identifierForVendor,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('获取设备信息失败: $e');
      }
    }
    
    return {};
  }

  /// 检查是否为平板设备
  Future<bool> isTablet() async {
    if (!_isInitialized) await initialize();
    
    try {
      if (_isAndroid) {
        final androidInfo = await _deviceInfo!.androidInfo;
        // 简单的平板检测逻辑
        return androidInfo.model.toLowerCase().contains('tablet') ||
               androidInfo.model.toLowerCase().contains('pad');
      } else if (_isIOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        return iosInfo.model.toLowerCase().contains('ipad');
      }
    } catch (e) {
      if (kDebugMode) {
        print('检测平板设备失败: $e');
      }
    }
    
    return false;
  }

  /// 触觉反馈（轻量级）
  Future<void> lightImpact() async {
    await HapticFeedback.lightImpact();
  }

  /// 触觉反馈（中等）
  Future<void> mediumImpact() async {
    await HapticFeedback.mediumImpact();
  }

  /// 触觉反馈（重量级）
  Future<void> heavyImpact() async {
    await HapticFeedback.heavyImpact();
  }

  /// 选择反馈
  Future<void> selectionClick() async {
    await HapticFeedback.selectionClick();
  }

  /// 组合反馈（震动 + 触觉）
  Future<void> combinedFeedback({
    VibrationPattern vibrationPattern = VibrationPattern.light,
    bool useHaptic = true,
  }) async {
    if (useHaptic) {
      await lightImpact();
    }
    
    await vibrate(pattern: vibrationPattern);
  }
}

/// 震动模式枚举
enum VibrationPattern {
  light,    // 轻微震动
  medium,   // 中等震动
  heavy,    // 强烈震动
  success,  // 成功模式
  error,    // 错误模式
  warning,  // 警告模式
  custom,   // 自定义
}

/// 移动特性配置
class MobileFeaturesConfig {
  final bool enableVibration;
  final bool enableHapticFeedback;
  final bool enableSystemShare;
  final VibrationPattern defaultVibrationPattern;

  const MobileFeaturesConfig({
    this.enableVibration = true,
    this.enableHapticFeedback = true,
    this.enableSystemShare = true,
    this.defaultVibrationPattern = VibrationPattern.light,
  });
}

/// 分享数据模型
class ShareData {
  final String title;
  final String? text;
  final String? url;
  final List<String>? files;
  final String? subject;

  const ShareData({
    required this.title,
    this.text,
    this.url,
    this.files,
    this.subject,
  });

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'text': text,
      'url': url,
      'files': files,
      'subject': subject,
    };
  }
}
