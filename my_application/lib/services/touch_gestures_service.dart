import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;

import 'mobile_features_service.dart';

/// 触摸手势服务
class TouchGesturesService {
  static final TouchGesturesService _instance = TouchGesturesService._internal();
  factory TouchGesturesService() => _instance;
  TouchGesturesService._internal();

  final MobileFeaturesService _mobileFeatures = MobileFeaturesService();

  /// 创建手势检测器
  GestureDetector createGestureDetector({
    required Widget child,
    TouchGestureOptions? options,
  }) {
    final gestureOptions = options ?? const TouchGestureOptions();
    
    return GestureDetector(
      onTap: gestureOptions.onTap,
      onDoubleTap: gestureOptions.onDoubleTap,
      onLongPress: () async {
        if (gestureOptions.enableVibration) {
          await _mobileFeatures.combinedFeedback(
            vibrationPattern: VibrationPattern.medium,
          );
        }
        gestureOptions.onLongPress?.call();
      },
      onPanStart: (details) {
        gestureOptions.onSwipeStart?.call(details);
      },
      onPanUpdate: (details) {
        gestureOptions.onSwipeUpdate?.call(details);
      },
      onPanEnd: (details) {
        _handleSwipeEnd(details, gestureOptions);
      },
      onScaleStart: (details) {
        gestureOptions.onPinchStart?.call(details);
      },
      onScaleUpdate: (details) {
        gestureOptions.onPinchUpdate?.call(details);
      },
      onScaleEnd: (details) {
        gestureOptions.onPinchEnd?.call(details);
      },
      child: child,
    );
  }

  void _handleSwipeEnd(DragEndDetails details, TouchGestureOptions options) {
    final velocity = details.velocity.pixelsPerSecond;
    final speed = velocity.distance;
    
    if (speed < options.swipeThreshold) return;

    final direction = _getSwipeDirection(velocity);
    
    if (options.enableVibration) {
      _mobileFeatures.combinedFeedback(
        vibrationPattern: VibrationPattern.light,
      );
    }

    switch (direction) {
      case SwipeDirection.left:
        options.onSwipeLeft?.call(details);
        break;
      case SwipeDirection.right:
        options.onSwipeRight?.call(details);
        break;
      case SwipeDirection.up:
        options.onSwipeUp?.call(details);
        break;
      case SwipeDirection.down:
        options.onSwipeDown?.call(details);
        break;
    }
  }

  SwipeDirection _getSwipeDirection(Offset velocity) {
    final dx = velocity.dx.abs();
    final dy = velocity.dy.abs();
    
    if (dx > dy) {
      return velocity.dx > 0 ? SwipeDirection.right : SwipeDirection.left;
    } else {
      return velocity.dy > 0 ? SwipeDirection.down : SwipeDirection.up;
    }
  }

  /// 计算两点之间的距离
  double calculateDistance(Offset point1, Offset point2) {
    return math.sqrt(
      math.pow(point2.dx - point1.dx, 2) + math.pow(point2.dy - point1.dy, 2),
    );
  }

  /// 计算角度
  double calculateAngle(Offset center, Offset point) {
    return math.atan2(point.dy - center.dy, point.dx - center.dx);
  }

  /// 检测是否为点击手势
  bool isTapGesture(Offset startPosition, Offset endPosition, Duration duration) {
    const maxDistance = 10.0;
    const maxDuration = Duration(milliseconds: 300);
    
    final distance = calculateDistance(startPosition, endPosition);
    return distance <= maxDistance && duration <= maxDuration;
  }

  /// 检测是否为长按手势
  bool isLongPressGesture(Duration duration) {
    const minDuration = Duration(milliseconds: 500);
    return duration >= minDuration;
  }

  /// 检测是否为滑动手势
  bool isSwipeGesture(Offset startPosition, Offset endPosition, Duration duration) {
    const minDistance = 50.0;
    const maxDuration = Duration(milliseconds: 300);
    
    final distance = calculateDistance(startPosition, endPosition);
    return distance >= minDistance && duration <= maxDuration;
  }
}

/// 触摸手势选项
class TouchGestureOptions {
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final Function(DragStartDetails)? onSwipeStart;
  final Function(DragUpdateDetails)? onSwipeUpdate;
  final Function(DragEndDetails)? onSwipeLeft;
  final Function(DragEndDetails)? onSwipeRight;
  final Function(DragEndDetails)? onSwipeUp;
  final Function(DragEndDetails)? onSwipeDown;
  final Function(ScaleStartDetails)? onPinchStart;
  final Function(ScaleUpdateDetails)? onPinchUpdate;
  final Function(ScaleEndDetails)? onPinchEnd;
  final double swipeThreshold;
  final Duration longPressDelay;
  final bool enableVibration;

  const TouchGestureOptions({
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onSwipeStart,
    this.onSwipeUpdate,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.onSwipeUp,
    this.onSwipeDown,
    this.onPinchStart,
    this.onPinchUpdate,
    this.onPinchEnd,
    this.swipeThreshold = 100.0,
    this.longPressDelay = const Duration(milliseconds: 500),
    this.enableVibration = true,
  });
}

/// 滑动方向枚举
enum SwipeDirection {
  left,
  right,
  up,
  down,
}

/// 手势事件数据
class GestureEvent {
  final GestureType type;
  final Offset position;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const GestureEvent({
    required this.type,
    required this.position,
    required this.timestamp,
    this.data = const {},
  });
}

/// 手势类型枚举
enum GestureType {
  tap,
  doubleTap,
  longPress,
  swipeLeft,
  swipeRight,
  swipeUp,
  swipeDown,
  pinchStart,
  pinchUpdate,
  pinchEnd,
}

/// 高级手势检测器
class AdvancedGestureDetector extends StatefulWidget {
  final Widget child;
  final TouchGestureOptions options;
  final Function(GestureEvent)? onGestureEvent;

  const AdvancedGestureDetector({
    super.key,
    required this.child,
    required this.options,
    this.onGestureEvent,
  });

  @override
  State<AdvancedGestureDetector> createState() => _AdvancedGestureDetectorState();
}

class _AdvancedGestureDetectorState extends State<AdvancedGestureDetector> {
  final TouchGesturesService _gestureService = TouchGesturesService();
  
  Offset? _startPosition;
  DateTime? _startTime;
  Timer? _longPressTimer;
  DateTime? _lastTapTime;

  void _emitGestureEvent(GestureType type, Offset position, [Map<String, dynamic>? data]) {
    final event = GestureEvent(
      type: type,
      position: position,
      timestamp: DateTime.now(),
      data: data ?? {},
    );
    
    widget.onGestureEvent?.call(event);
  }

  void _handleTapDown(TapDownDetails details) {
    _startPosition = details.localPosition;
    _startTime = DateTime.now();
    
    // 设置长按定时器
    _longPressTimer = Timer(widget.options.longPressDelay, () {
      if (_startPosition != null) {
        _emitGestureEvent(GestureType.longPress, _startPosition!);
        widget.options.onLongPress?.call();
      }
    });
  }

  void _handleTapUp(TapUpDetails details) {
    _longPressTimer?.cancel();
    
    if (_startPosition != null && _startTime != null) {
      final duration = DateTime.now().difference(_startTime!);
      final distance = _gestureService.calculateDistance(_startPosition!, details.localPosition);
      
      if (_gestureService.isTapGesture(_startPosition!, details.localPosition, duration)) {
        final now = DateTime.now();
        
        // 检测双击
        if (_lastTapTime != null && now.difference(_lastTapTime!).inMilliseconds < 300) {
          _emitGestureEvent(GestureType.doubleTap, details.localPosition);
          widget.options.onDoubleTap?.call();
          _lastTapTime = null;
        } else {
          _lastTapTime = now;
          
          // 延迟执行单击，以便检测双击
          Timer(const Duration(milliseconds: 300), () {
            if (_lastTapTime == now) {
              _emitGestureEvent(GestureType.tap, details.localPosition);
              widget.options.onTap?.call();
            }
          });
        }
      }
    }
    
    _startPosition = null;
    _startTime = null;
  }

  void _handlePanEnd(DragEndDetails details) {
    _longPressTimer?.cancel();
    
    if (_startPosition != null && _startTime != null) {
      final velocity = details.velocity.pixelsPerSecond;
      final speed = velocity.distance;
      
      if (speed >= widget.options.swipeThreshold) {
        final direction = _gestureService._getSwipeDirection(velocity);
        
        switch (direction) {
          case SwipeDirection.left:
            _emitGestureEvent(GestureType.swipeLeft, _startPosition!);
            widget.options.onSwipeLeft?.call(details);
            break;
          case SwipeDirection.right:
            _emitGestureEvent(GestureType.swipeRight, _startPosition!);
            widget.options.onSwipeRight?.call(details);
            break;
          case SwipeDirection.up:
            _emitGestureEvent(GestureType.swipeUp, _startPosition!);
            widget.options.onSwipeUp?.call(details);
            break;
          case SwipeDirection.down:
            _emitGestureEvent(GestureType.swipeDown, _startPosition!);
            widget.options.onSwipeDown?.call(details);
            break;
        }
      }
    }
    
    _startPosition = null;
    _startTime = null;
  }

  @override
  void dispose() {
    _longPressTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onPanStart: widget.options.onSwipeStart,
      onPanUpdate: widget.options.onSwipeUpdate,
      onPanEnd: _handlePanEnd,
      onScaleStart: (details) {
        _emitGestureEvent(GestureType.pinchStart, details.localFocalPoint);
        widget.options.onPinchStart?.call(details);
      },
      onScaleUpdate: (details) {
        _emitGestureEvent(GestureType.pinchUpdate, details.localFocalPoint, {
          'scale': details.scale,
          'rotation': details.rotation,
        });
        widget.options.onPinchUpdate?.call(details);
      },
      onScaleEnd: (details) {
        _emitGestureEvent(GestureType.pinchEnd, details.localFocalPoint);
        widget.options.onPinchEnd?.call(details);
      },
      child: widget.child,
    );
  }
}
