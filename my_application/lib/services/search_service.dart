import 'package:shared_preferences/shared_preferences.dart';
import '../models/marker_model.dart';
import '../services/marker_service.dart';

class SearchService {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  final MarkerService _markerService = MarkerService();
  static const String _searchHistoryKey = 'search_history';
  static const int _maxHistoryItems = 20;

  // 搜索历史
  List<String> _searchHistory = [];
  List<String> get searchHistory => List.unmodifiable(_searchHistory);

  // 搜索建议
  List<String> _searchSuggestions = [];
  List<String> get searchSuggestions => List.unmodifiable(_searchSuggestions);

  /// 初始化搜索服务
  Future<void> initialize() async {
    await _loadSearchHistory();
    await _generateSearchSuggestions();
  }

  /// 执行搜索
  Future<List<MarkerModel>> search(String query) async {
    if (query.trim().isEmpty) {
      return await _markerService.getAllMarkers();
    }

    // 添加到搜索历史
    await _addToSearchHistory(query.trim());

    // 执行搜索
    return await _markerService.searchMarkers(query.trim());
  }

  /// 高级搜索
  Future<List<MarkerModel>> advancedSearch({
    String? query,
    String? category,
    List<String>? tags,
    int? minRating,
    bool? isPublic,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    List<MarkerModel> results = await _markerService.getAllMarkers();

    // 文本搜索
    if (query != null && query.trim().isNotEmpty) {
      final searchQuery = query.trim().toLowerCase();
      results = results.where((marker) {
        final titleMatch = marker.title.toLowerCase().contains(searchQuery);
        final descriptionMatch = marker.description?.toLowerCase().contains(searchQuery) ?? false;
        final tagsMatch = marker.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
        final addressMatch = marker.address?.toLowerCase().contains(searchQuery) ?? false;
        
        return titleMatch || descriptionMatch || tagsMatch || addressMatch;
      }).toList();
    }

    // 分类筛选
    if (category != null && category.isNotEmpty) {
      results = results.where((marker) => marker.category == category).toList();
    }

    // 标签筛选
    if (tags != null && tags.isNotEmpty) {
      results = results.where((marker) {
        return tags.any((tag) => marker.tags.contains(tag));
      }).toList();
    }

    // 评分筛选
    if (minRating != null) {
      results = results.where((marker) {
        return marker.rating != null && marker.rating! >= minRating;
      }).toList();
    }

    // 隐私筛选
    if (isPublic != null) {
      results = results.where((marker) => marker.isPublic == isPublic).toList();
    }

    // 日期筛选
    if (startDate != null) {
      results = results.where((marker) {
        return marker.createdAt.isAfter(startDate) || 
               marker.createdAt.isAtSameMomentAs(startDate);
      }).toList();
    }

    if (endDate != null) {
      results = results.where((marker) {
        return marker.createdAt.isBefore(endDate) || 
               marker.createdAt.isAtSameMomentAs(endDate);
      }).toList();
    }

    return results;
  }

  /// 获取搜索建议
  List<String> getSearchSuggestions(String query) {
    if (query.trim().isEmpty) {
      return _searchSuggestions.take(10).toList();
    }

    final lowerQuery = query.toLowerCase();
    final suggestions = <String>[];

    // 从搜索历史中匹配
    for (final history in _searchHistory) {
      if (history.toLowerCase().contains(lowerQuery)) {
        suggestions.add(history);
      }
    }

    // 从预设建议中匹配
    for (final suggestion in _searchSuggestions) {
      if (suggestion.toLowerCase().contains(lowerQuery) && 
          !suggestions.contains(suggestion)) {
        suggestions.add(suggestion);
      }
    }

    return suggestions.take(10).toList();
  }

  /// 获取热门搜索词
  Future<List<String>> getPopularSearchTerms() async {
    // 基于搜索历史频率返回热门搜索词
    final termFrequency = <String, int>{};
    
    for (final term in _searchHistory) {
      termFrequency[term] = (termFrequency[term] ?? 0) + 1;
    }

    final sortedTerms = termFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedTerms.take(10).map((e) => e.key).toList();
  }

  /// 清除搜索历史
  Future<void> clearSearchHistory() async {
    _searchHistory.clear();
    await _saveSearchHistory();
  }

  /// 删除特定搜索历史项
  Future<void> removeSearchHistoryItem(String item) async {
    _searchHistory.remove(item);
    await _saveSearchHistory();
  }

  /// 添加到搜索历史
  Future<void> _addToSearchHistory(String query) async {
    if (query.isEmpty) return;

    // 移除重复项
    _searchHistory.remove(query);
    
    // 添加到开头
    _searchHistory.insert(0, query);
    
    // 限制历史记录数量
    if (_searchHistory.length > _maxHistoryItems) {
      _searchHistory = _searchHistory.take(_maxHistoryItems).toList();
    }

    await _saveSearchHistory();
  }

  /// 加载搜索历史
  Future<void> _loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_searchHistoryKey) ?? [];
      _searchHistory = history;
    } catch (e) {
      print('Error loading search history: $e');
      _searchHistory = [];
    }
  }

  /// 保存搜索历史
  Future<void> _saveSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_searchHistoryKey, _searchHistory);
    } catch (e) {
      print('Error saving search history: $e');
    }
  }

  /// 生成搜索建议
  Future<void> _generateSearchSuggestions() async {
    try {
      final markers = await _markerService.getAllMarkers();
      final suggestions = <String>{};

      // 从标记点中提取建议
      for (final marker in markers) {
        // 添加标题
        suggestions.add(marker.title);
        
        // 添加分类
        suggestions.add(marker.category);
        
        // 添加标签
        suggestions.addAll(marker.tags);
        
        // 添加地址关键词
        if (marker.address != null) {
          final addressWords = marker.address!.split(RegExp(r'[\s,，。]+'));
          for (final word in addressWords) {
            if (word.length > 1) {
              suggestions.add(word);
            }
          }
        }
      }

      // 添加常用搜索词
      suggestions.addAll([
        '美食', '咖啡', '购物', '景点', '休闲', '娱乐',
        '运动', '学习', '工作', '聚会', '约会', '家庭',
        '推荐', '必去', '网红', '特色', '便宜', '高档',
        '附近', '新开', '老店', '连锁', '独立',
      ]);

      _searchSuggestions = suggestions.toList()..sort();
    } catch (e) {
      print('Error generating search suggestions: $e');
      _searchSuggestions = [];
    }
  }

  /// 刷新搜索建议
  Future<void> refreshSearchSuggestions() async {
    await _generateSearchSuggestions();
  }
}
