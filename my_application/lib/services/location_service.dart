import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:async';

/// 位置服务类
/// 提供统一的位置获取、权限管理和地理编码功能
class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  Position? _lastKnownPosition;
  StreamSubscription<Position>? _positionStreamSubscription;
  final StreamController<Position> _positionController = StreamController<Position>.broadcast();
  
  bool _isLocationServiceEnabled = false;
  LocationPermission _currentPermission = LocationPermission.denied;

  /// 位置更新流
  Stream<Position> get positionStream => _positionController.stream;

  /// 最后已知位置
  Position? get lastKnownPosition => _lastKnownPosition;

  /// 位置服务是否启用
  bool get isLocationServiceEnabled => _isLocationServiceEnabled;

  /// 当前权限状态
  LocationPermission get currentPermission => _currentPermission;

  /// 初始化位置服务
  Future<bool> initialize() async {
    try {
      // 检查位置服务是否启用
      _isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
      
      if (!_isLocationServiceEnabled) {
        if (kDebugMode) {
          print('位置服务未启用');
        }
        return false;
      }

      // 检查权限
      _currentPermission = await Geolocator.checkPermission();
      
      if (kDebugMode) {
        print('位置服务初始化完成 - 权限状态: $_currentPermission');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('位置服务初始化失败: $e');
      }
      return false;
    }
  }

  /// 请求位置权限
  Future<LocationPermission> requestPermission() async {
    try {
      if (_currentPermission == LocationPermission.denied) {
        _currentPermission = await Geolocator.requestPermission();
      }
      
      if (kDebugMode) {
        print('位置权限请求结果: $_currentPermission');
      }
      
      return _currentPermission;
    } catch (e) {
      if (kDebugMode) {
        print('请求位置权限失败: $e');
      }
      return LocationPermission.denied;
    }
  }

  /// 检查权限是否已授予
  bool get hasPermission {
    return _currentPermission == LocationPermission.always ||
           _currentPermission == LocationPermission.whileInUse;
  }

  /// 获取当前位置
  Future<Position> getCurrentPosition({
    LocationAccuracy accuracy = LocationAccuracy.high,
    Duration? timeLimit,
  }) async {
    // 确保权限已授予
    if (!hasPermission) {
      await requestPermission();
      if (!hasPermission) {
        throw LocationServiceException('位置权限未授予');
      }
    }

    // 检查位置服务是否启用
    if (!_isLocationServiceEnabled) {
      _isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!_isLocationServiceEnabled) {
        throw LocationServiceException('位置服务未启用');
      }
    }

    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: timeLimit ?? const Duration(seconds: 15),
      );

      _lastKnownPosition = position;
      _positionController.add(position);

      if (kDebugMode) {
        print('获取当前位置成功: ${position.latitude}, ${position.longitude}');
      }

      return position;
    } catch (e) {
      if (kDebugMode) {
        print('获取当前位置失败: $e');
      }
      
      // 如果获取失败，尝试返回最后已知位置
      if (_lastKnownPosition != null) {
        if (kDebugMode) {
          print('返回最后已知位置');
        }
        return _lastKnownPosition!;
      }
      
      throw LocationServiceException('无法获取位置信息: $e');
    }
  }

  /// 开始位置监听
  void startLocationUpdates({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10, // 最小距离变化（米）
    Duration? interval,
  }) {
    if (_positionStreamSubscription != null) {
      stopLocationUpdates();
    }

    final locationSettings = LocationSettings(
      accuracy: accuracy,
      distanceFilter: distanceFilter,
    );

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen(
      (Position position) {
        _lastKnownPosition = position;
        _positionController.add(position);
        
        if (kDebugMode) {
          print('位置更新: ${position.latitude}, ${position.longitude}');
        }
      },
      onError: (error) {
        if (kDebugMode) {
          print('位置监听错误: $error');
        }
      },
    );

    if (kDebugMode) {
      print('开始位置监听');
    }
  }

  /// 停止位置监听
  void stopLocationUpdates() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    
    if (kDebugMode) {
      print('停止位置监听');
    }
  }

  /// 计算两点之间的距离（米）
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// 计算两点之间的方位角（度）
  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// 地理编码：坐标转地址
  Future<List<Placemark>> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (kDebugMode) {
        print('地理编码成功: ${placemarks.length} 个结果');
      }
      
      return placemarks;
    } catch (e) {
      if (kDebugMode) {
        print('地理编码失败: $e');
      }
      throw LocationServiceException('地理编码失败: $e');
    }
  }

  /// 反向地理编码：地址转坐标
  Future<List<Location>> getCoordinatesFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      
      if (kDebugMode) {
        print('反向地理编码成功: ${locations.length} 个结果');
      }
      
      return locations;
    } catch (e) {
      if (kDebugMode) {
        print('反向地理编码失败: $e');
      }
      throw LocationServiceException('反向地理编码失败: $e');
    }
  }

  /// 获取格式化的地址字符串
  Future<String> getFormattedAddress(double latitude, double longitude) async {
    try {
      final placemarks = await getAddressFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final addressParts = <String>[];
        
        if (placemark.street != null && placemark.street!.isNotEmpty) {
          addressParts.add(placemark.street!);
        }
        if (placemark.subLocality != null && placemark.subLocality!.isNotEmpty) {
          addressParts.add(placemark.subLocality!);
        }
        if (placemark.locality != null && placemark.locality!.isNotEmpty) {
          addressParts.add(placemark.locality!);
        }
        if (placemark.administrativeArea != null && placemark.administrativeArea!.isNotEmpty) {
          addressParts.add(placemark.administrativeArea!);
        }
        if (placemark.country != null && placemark.country!.isNotEmpty) {
          addressParts.add(placemark.country!);
        }
        
        return addressParts.join(', ');
      }
      
      return '未知地址';
    } catch (e) {
      return '地址解析失败';
    }
  }

  /// 检查是否在指定区域内
  bool isWithinArea(
    double latitude,
    double longitude,
    double centerLatitude,
    double centerLongitude,
    double radiusInMeters,
  ) {
    final distance = calculateDistance(
      latitude,
      longitude,
      centerLatitude,
      centerLongitude,
    );
    
    return distance <= radiusInMeters;
  }

  /// 获取位置精度描述
  String getAccuracyDescription(double accuracy) {
    if (accuracy <= 5) {
      return '高精度';
    } else if (accuracy <= 10) {
      return '中等精度';
    } else if (accuracy <= 50) {
      return '低精度';
    } else {
      return '精度较差';
    }
  }

  /// 释放资源
  void dispose() {
    stopLocationUpdates();
    _positionController.close();
    
    if (kDebugMode) {
      print('位置服务已释放');
    }
  }
}

/// 位置服务异常类
class LocationServiceException implements Exception {
  final String message;
  
  const LocationServiceException(this.message);
  
  @override
  String toString() => 'LocationServiceException: $message';
}

/// 位置权限状态扩展
extension LocationPermissionExtension on LocationPermission {
  String get description {
    switch (this) {
      case LocationPermission.denied:
        return '权限被拒绝';
      case LocationPermission.deniedForever:
        return '权限被永久拒绝';
      case LocationPermission.whileInUse:
        return '使用时允许';
      case LocationPermission.always:
        return '始终允许';
      case LocationPermission.unableToDetermine:
        return '无法确定权限状态';
    }
  }
  
  bool get isGranted {
    return this == LocationPermission.always || this == LocationPermission.whileInUse;
  }
}
