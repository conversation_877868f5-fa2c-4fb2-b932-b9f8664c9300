import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';

import '../models/marker_model.dart';
import '../constants/app_constants.dart';
import '../services/marker_service.dart';

class ShareService {
  static final ShareService _instance = ShareService._internal();
  factory ShareService() => _instance;
  ShareService._internal();

  final MarkerService _markerService = MarkerService();
  final Uuid _uuid = const Uuid();

  /// 分享单个标记点
  Future<void> shareMarker(MarkerModel marker, {String? customMessage}) async {
    try {
      final shareText = _generateMarkerShareText(marker, customMessage);
      
      if (marker.imagePaths.isNotEmpty) {
        // 如果有图片，分享图片和文本
        final files = marker.imagePaths.map((path) => XFile(path)).toList();
        await Share.shareXFiles(files, text: shareText);
      } else {
        // 只分享文本
        await Share.share(shareText, subject: marker.title);
      }
    } catch (e) {
      throw Exception('分享失败: $e');
    }
  }

  /// 分享多个标记点
  Future<void> shareMultipleMarkers(
    List<MarkerModel> markers, {
    String? customMessage,
  }) async {
    try {
      final shareText = _generateMultipleMarkersShareText(markers, customMessage);
      await Share.share(shareText, subject: '我的收藏地点');
    } catch (e) {
      throw Exception('分享失败: $e');
    }
  }

  /// 生成分享链接
  Future<String> generateShareLink(MarkerModel marker) async {
    try {
      // 如果标记点还没有分享ID，生成一个
      if (marker.shareId == null) {
        final shareId = _uuid.v4();
        final updatedMarker = marker.copyWith(
          shareId: shareId,
          isPublic: true,
        );
        await _markerService.updateMarker(updatedMarker);
        return '${AppConstants.shareBaseUrl}$shareId';
      }
      
      return '${AppConstants.shareBaseUrl}${marker.shareId}';
    } catch (e) {
      throw Exception('生成分享链接失败: $e');
    }
  }

  /// 生成二维码
  Future<String> generateQRCode(String content) async {
    try {
      final qrValidationResult = QrValidator.validate(
        data: content,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
      );

      if (qrValidationResult.status == QrValidationStatus.valid) {
        final qrCode = qrValidationResult.qrCode!;
        
        // 生成二维码图片并保存到临时目录
        final tempDir = await getTemporaryDirectory();
        final qrImagePath = '${tempDir.path}/qr_${_uuid.v4()}.png';
        
        // TODO: 实际生成二维码图片文件
        // 这里需要使用 qr_flutter 的 QrPainter 来生成图片
        
        return qrImagePath;
      } else {
        throw Exception('二维码数据无效');
      }
    } catch (e) {
      throw Exception('生成二维码失败: $e');
    }
  }

  /// 复制到剪贴板
  Future<void> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
    } catch (e) {
      throw Exception('复制失败: $e');
    }
  }

  /// 分享应用
  Future<void> shareApp() async {
    const shareText = '''
${AppConstants.appNameChinese} - 我的喜好地图

一个基于地理位置的个人兴趣收藏工具

特色功能：
• 隐私优先，本地存储
• 支持多种分类和标签
• 可选的云端同步
• Material Design 3 设计

快来下载体验吧！
''';
    
    await Share.share(shareText, subject: AppConstants.appNameChinese);
  }

  /// 导出数据为文件并分享
  Future<void> exportAndShare(List<MarkerModel> markers) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = 'MyMapFavs_Export_${DateTime.now().millisecondsSinceEpoch}.json';
      final filePath = '${tempDir.path}/$fileName';
      
      final exportData = {
        'app': AppConstants.appNameChinese,
        'version': AppConstants.appVersion,
        'exportTime': DateTime.now().toIso8601String(),
        'totalCount': markers.length,
        'markers': markers.map((m) => m.toJson()).toList(),
      };
      
      final file = File(filePath);
      await file.writeAsString(
        const JsonEncoder.withIndent('  ').convert(exportData),
      );
      
      await Share.shareXFiles(
        [XFile(filePath)],
        text: '我的地图收藏数据导出文件',
        subject: 'MyMapFavs 数据导出',
      );
    } catch (e) {
      throw Exception('导出分享失败: $e');
    }
  }

  /// 生成标记点分享文本
  String _generateMarkerShareText(MarkerModel marker, String? customMessage) {
    final buffer = StringBuffer();
    
    if (customMessage != null && customMessage.isNotEmpty) {
      buffer.writeln(customMessage);
      buffer.writeln();
    }
    
    buffer.writeln('📍 ${marker.title}');
    
    if (marker.description != null && marker.description!.isNotEmpty) {
      buffer.writeln(marker.description);
    }
    
    buffer.writeln();
    buffer.writeln('🏷️ 分类: ${marker.category}');
    
    if (marker.rating != null) {
      final stars = '⭐' * marker.rating!;
      buffer.writeln('⭐ 评分: $stars (${marker.rating}/5)');
    }
    
    if (marker.tags.isNotEmpty) {
      buffer.writeln('🏷️ 标签: ${marker.tags.map((tag) => '#$tag').join(' ')}');
    }
    
    if (marker.address != null && marker.address!.isNotEmpty) {
      buffer.writeln('📍 地址: ${marker.address}');
    }
    
    buffer.writeln('🌍 坐标: ${marker.latitude.toStringAsFixed(6)}, ${marker.longitude.toStringAsFixed(6)}');
    
    buffer.writeln();
    buffer.writeln('来自 ${AppConstants.appNameChinese}');
    
    return buffer.toString();
  }

  /// 生成多个标记点分享文本
  String _generateMultipleMarkersShareText(
    List<MarkerModel> markers,
    String? customMessage,
  ) {
    final buffer = StringBuffer();
    
    if (customMessage != null && customMessage.isNotEmpty) {
      buffer.writeln(customMessage);
      buffer.writeln();
    }
    
    buffer.writeln('📍 我的收藏地点 (${markers.length}个)');
    buffer.writeln();
    
    for (int i = 0; i < markers.length; i++) {
      final marker = markers[i];
      buffer.writeln('${i + 1}. ${marker.title}');
      
      if (marker.description != null && marker.description!.isNotEmpty) {
        buffer.writeln('   ${marker.description}');
      }
      
      buffer.writeln('   🏷️ ${marker.category}');
      
      if (marker.rating != null) {
        final stars = '⭐' * marker.rating!;
        buffer.writeln('   ⭐ $stars');
      }
      
      buffer.writeln();
    }
    
    buffer.writeln('来自 ${AppConstants.appNameChinese}');
    
    return buffer.toString();
  }

  /// 获取分享选项
  List<ShareOption> getShareOptions() {
    return [
      ShareOption(
        title: '分享文本',
        icon: Icons.text_fields,
        action: ShareAction.text,
      ),
      ShareOption(
        title: '生成链接',
        icon: Icons.link,
        action: ShareAction.link,
      ),
      ShareOption(
        title: '生成二维码',
        icon: Icons.qr_code,
        action: ShareAction.qrCode,
      ),
      ShareOption(
        title: '复制到剪贴板',
        icon: Icons.copy,
        action: ShareAction.clipboard,
      ),
      ShareOption(
        title: '导出文件',
        icon: Icons.file_download,
        action: ShareAction.export,
      ),
    ];
  }
}

/// 分享选项
class ShareOption {
  final String title;
  final IconData icon;
  final ShareAction action;

  ShareOption({
    required this.title,
    required this.icon,
    required this.action,
  });
}

/// 分享动作枚举
enum ShareAction {
  text,
  link,
  qrCode,
  clipboard,
  export,
}
