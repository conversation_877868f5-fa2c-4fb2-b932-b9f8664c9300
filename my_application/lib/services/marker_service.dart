import 'dart:io';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/marker_model.dart';
import '../models/category_model.dart';
import '../utils/database_helper.dart';

class MarkerService {
  static final MarkerService _instance = MarkerService._internal();
  factory MarkerService() => _instance;
  MarkerService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final Uuid _uuid = const Uuid();

  // 标记点操作
  Future<int> createMarker(MarkerModel marker) async {
    final markerWithId = marker.copyWith(
      createdAt: DateTime.now(),
      shareId: marker.isPublic ? _uuid.v4() : null,
    );
    return await _dbHelper.insertMarker(markerWithId);
  }

  Future<List<MarkerModel>> getAllMarkers() async {
    return await _dbHelper.getAllMarkers();
  }

  Future<MarkerModel?> getMarkerById(int id) async {
    return await _dbHelper.getMarkerById(id);
  }

  Future<int> updateMarker(MarkerModel marker) async {
    final updatedMarker = marker.copyWith(
      updatedAt: DateTime.now(),
      shareId: marker.isPublic && marker.shareId == null ? _uuid.v4() : marker.shareId,
    );
    return await _dbHelper.updateMarker(updatedMarker);
  }

  Future<int> deleteMarker(int id) async {
    // 删除相关文件
    final marker = await _dbHelper.getMarkerById(id);
    if (marker != null) {
      await _deleteMarkerFiles(marker);
    }
    return await _dbHelper.deleteMarker(id);
  }

  Future<List<MarkerModel>> getMarkersByCategory(String category) async {
    return await _dbHelper.getMarkersByCategory(category);
  }

  Future<List<MarkerModel>> searchMarkers(String searchTerm) async {
    return await _dbHelper.searchMarkersWithModel(searchTerm);
  }

  Future<List<MarkerModel>> getPublicMarkers() async {
    return await _dbHelper.getPublicMarkers();
  }

  Future<MarkerModel?> getMarkerByShareId(String shareId) async {
    return await _dbHelper.getMarkerByShareId(shareId);
  }

  // 分类操作
  Future<List<CategoryModel>> getAllCategories() async {
    return await _dbHelper.getAllCategories();
  }

  Future<int> createCategory(CategoryModel category) async {
    return await _dbHelper.insertCategory(category);
  }

  Future<int> updateCategory(CategoryModel category) async {
    return await _dbHelper.updateCategory(category);
  }

  Future<int> deleteCategory(int id) async {
    return await _dbHelper.deleteCategory(id);
  }

  // 文件管理
  Future<String> saveImage(String sourcePath, int markerId) async {
    final appDir = await getApplicationDocumentsDirectory();
    final markerDir = Directory(path.join(appDir.path, 'markers', markerId.toString()));
    
    if (!await markerDir.exists()) {
      await markerDir.create(recursive: true);
    }

    final fileName = '${_uuid.v4()}.jpg';
    final targetPath = path.join(markerDir.path, fileName);
    
    final sourceFile = File(sourcePath);
    await sourceFile.copy(targetPath);
    
    return targetPath;
  }

  Future<String> saveAudio(String sourcePath, int markerId) async {
    final appDir = await getApplicationDocumentsDirectory();
    final markerDir = Directory(path.join(appDir.path, 'markers', markerId.toString()));
    
    if (!await markerDir.exists()) {
      await markerDir.create(recursive: true);
    }

    final fileName = '${_uuid.v4()}.m4a';
    final targetPath = path.join(markerDir.path, fileName);
    
    final sourceFile = File(sourcePath);
    await sourceFile.copy(targetPath);
    
    return targetPath;
  }

  Future<void> _deleteMarkerFiles(MarkerModel marker) async {
    try {
      // 删除图片文件
      for (final imagePath in marker.imagePaths) {
        final file = File(imagePath);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // 删除音频文件
      if (marker.audioPath != null) {
        final file = File(marker.audioPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // 删除标记点目录（如果为空）
      if (marker.id != null) {
        final appDir = await getApplicationDocumentsDirectory();
        final markerDir = Directory(path.join(appDir.path, 'markers', marker.id.toString()));
        if (await markerDir.exists()) {
          final files = await markerDir.list().toList();
          if (files.isEmpty) {
            await markerDir.delete();
          }
        }
      }
    } catch (e) {
      print('Error deleting marker files: $e');
    }
  }

  // 统计信息
  Future<Map<String, int>> getStatistics() async {
    return await _dbHelper.getStatistics();
  }

  // 数据导出
  Future<List<Map<String, dynamic>>> exportData() async {
    final markers = await getAllMarkers();
    return markers.map((marker) => marker.toJson()).toList();
  }

  // 批量操作
  Future<void> deleteMultipleMarkers(List<int> ids) async {
    for (final id in ids) {
      await deleteMarker(id);
    }
  }

  Future<void> updateMultipleMarkersPrivacy(List<int> ids, bool isPublic) async {
    for (final id in ids) {
      final marker = await getMarkerById(id);
      if (marker != null) {
        await updateMarker(marker.copyWith(isPublic: isPublic));
      }
    }
  }

  // 清理未使用的文件
  Future<void> cleanupUnusedFiles() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final markersDir = Directory(path.join(appDir.path, 'markers'));
      
      if (!await markersDir.exists()) return;

      final allMarkers = await getAllMarkers();
      final usedPaths = <String>{};
      
      for (final marker in allMarkers) {
        usedPaths.addAll(marker.imagePaths);
        if (marker.audioPath != null) {
          usedPaths.add(marker.audioPath!);
        }
      }

      await for (final entity in markersDir.list(recursive: true)) {
        if (entity is File && !usedPaths.contains(entity.path)) {
          await entity.delete();
        }
      }
    } catch (e) {
      print('Error cleaning up unused files: $e');
    }
  }
}
