import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../constants/app_constants.dart';

class MapService {
  static final MapService _instance = MapService._internal();
  factory MapService() => _instance;
  MapService._internal();

  bool _isInitialized = false;
  bool _initializationFailed = false;
  String? _errorMessage;

  bool get isInitialized => _isInitialized;
  bool get initializationFailed => _initializationFailed;
  String? get errorMessage => _errorMessage;

  /// 初始化地图服务
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    if (_initializationFailed) return false;

    try {
      // Google Maps 不需要特殊初始化，只需要在配置文件中设置 API Key
      _isInitialized = true;
      _initializationFailed = false;
      _errorMessage = null;
      
      if (kDebugMode) {
        print('地图服务初始化成功');
      }
      
      return true;
    } catch (e) {
      _isInitialized = false;
      _initializationFailed = true;
      _errorMessage = e.toString();
      
      if (kDebugMode) {
        print('地图服务初始化失败: $e');
      }
      
      return false;
    }
  }

  /// 检查地图服务是否可用
  bool isMapAvailable() {
    return _isInitialized && !_initializationFailed;
  }

  /// 获取错误信息用于显示给用户
  String getErrorMessage() {
    if (_initializationFailed) {
      return _errorMessage ?? '地图服务初始化失败';
    }
    return '';
  }

  /// 重试初始化
  Future<bool> retryInitialization() async {
    _isInitialized = false;
    _initializationFailed = false;
    _errorMessage = null;
    return await initialize();
  }
}
