import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

/// 地图服务类型枚举
enum MapServiceType {
  googleMaps,  // Google Maps
  amap,        // 高德地图
}

/// 地图服务管理器
/// 根据用户位置自动选择最适合的地图服务
class MapServiceManager {
  static final MapServiceManager _instance = MapServiceManager._internal();
  factory MapServiceManager() => _instance;
  MapServiceManager._internal();

  MapServiceType? _currentMapService;
  Position? _userPosition;
  bool _isInitialized = false;

  /// 获取当前地图服务类型
  MapServiceType get currentMapService => _currentMapService ?? MapServiceType.googleMaps;

  /// 获取用户位置
  Position? get userPosition => _userPosition;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化地图服务
  /// 根据用户位置自动选择地图服务
  Future<MapServiceType> initializeMapService() async {
    try {
      print('地图服务管理器：开始初始化');
      
      // 检查位置权限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('地图服务管理器：位置权限被拒绝，使用默认地图服务');
          return _setDefaultMapService();
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('地图服务管理器：位置权限永久拒绝，使用默认地图服务');
        return _setDefaultMapService();
      }

      // 获取用户当前位置
      _userPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      print('地图服务管理器：获取到用户位置 - 纬度: ${_userPosition!.latitude}, 经度: ${_userPosition!.longitude}');

      // 根据位置选择地图服务
      _currentMapService = await _selectMapServiceByLocation(_userPosition!);
      _isInitialized = true;

      print('地图服务管理器：选择的地图服务 - ${_currentMapService.toString()}');
      return _currentMapService!;

    } catch (e) {
      print('地图服务管理器：初始化失败 - $e');
      return _setDefaultMapService();
    }
  }

  /// 根据位置选择地图服务
  Future<MapServiceType> _selectMapServiceByLocation(Position position) async {
    try {
      // 检查是否在中国大陆范围内
      if (_isInMainlandChina(position.latitude, position.longitude)) {
        print('地图服务管理器：用户位于中国大陆，选择高德地图');
        return MapServiceType.amap;
      }

      // 尝试通过地理编码获取更准确的位置信息
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude, 
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        String? country = placemarks.first.country;
        String? isoCountryCode = placemarks.first.isoCountryCode;
        
        print('地图服务管理器：检测到国家 - $country ($isoCountryCode)');
        
        // 检查是否为中国
        if (country != null && 
            (country.toLowerCase().contains('china') || 
             country.contains('中国') ||
             isoCountryCode == 'CN')) {
          print('地图服务管理器：确认用户位于中国，选择高德地图');
          return MapServiceType.amap;
        }
      }

      print('地图服务管理器：用户位于中国以外地区，选择Google Maps');
      return MapServiceType.googleMaps;

    } catch (e) {
      print('地图服务管理器：位置检测失败 - $e，使用默认服务');
      return _getDefaultMapServiceByPlatform();
    }
  }

  /// 检查坐标是否在中国大陆范围内
  /// 使用粗略的边界框检查
  bool _isInMainlandChina(double latitude, double longitude) {
    // 中国大陆的大致边界
    // 纬度范围：18°N - 54°N
    // 经度范围：73°E - 135°E
    const double minLat = 18.0;
    const double maxLat = 54.0;
    const double minLng = 73.0;
    const double maxLng = 135.0;

    return latitude >= minLat && 
           latitude <= maxLat && 
           longitude >= minLng && 
           longitude <= maxLng;
  }

  /// 设置默认地图服务
  MapServiceType _setDefaultMapService() {
    _currentMapService = _getDefaultMapServiceByPlatform();
    _isInitialized = true;
    return _currentMapService!;
  }

  /// 根据平台获取默认地图服务
  MapServiceType _getDefaultMapServiceByPlatform() {
    // 在中国地区，默认使用高德地图
    // 在其他地区，默认使用Google Maps
    // 这里可以根据系统语言或其他信息进行判断
    return MapServiceType.amap; // 默认使用高德地图进行测试
  }

  /// 手动切换地图服务
  void switchMapService(MapServiceType newService) {
    if (_currentMapService != newService) {
      _currentMapService = newService;
      print('地图服务管理器：手动切换到 ${newService.toString()}');
    }
  }

  /// 获取地图服务显示名称
  String getMapServiceDisplayName(MapServiceType service) {
    switch (service) {
      case MapServiceType.googleMaps:
        return 'Google Maps';
      case MapServiceType.amap:
        return '高德地图';
    }
  }

  /// 重置管理器状态
  void reset() {
    _currentMapService = null;
    _userPosition = null;
    _isInitialized = false;
    print('地图服务管理器：状态已重置');
  }
}
