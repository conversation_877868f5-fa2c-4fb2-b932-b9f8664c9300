import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../models/marker_model.dart';
import '../models/category_model.dart';

class DatabaseHelper {
  static final _databaseName = "MyMapFavs.db";
  static final _databaseVersion = 2; // 增加版本号

  // 标记点表
  static final table = 'markers';
  static final columnId = '_id';
  static final columnTitle = 'title';
  static final columnDescription = 'description';
  static final columnLatitude = 'latitude';
  static final columnLongitude = 'longitude';
  static final columnCategory = 'category';
  static final columnImagePaths = 'image_paths'; // 改为复数，存储JSON数组
  static final columnAudioPath = 'audio_path'; // 新增音频路径
  static final columnTags = 'tags'; // 新增标签，存储JSON数组
  static final columnCreatedAt = 'created_at';
  static final columnUpdatedAt = 'updated_at'; // 新增更新时间
  static final columnIsPublic = 'is_public';
  static final columnShareId = 'share_id'; // 新增分享ID
  static final columnAddress = 'address'; // 新增地址
  static final columnRating = 'rating'; // 新增评分

  // 分类表
  static final categoryTable = 'categories';
  static final categoryColumnId = '_id';
  static final categoryColumnName = 'name';
  static final categoryColumnIcon = 'icon';
  static final categoryColumnColor = 'color';
  static final categoryColumnSortOrder = 'sort_order';
  static final categoryColumnIsDefault = 'is_default';
  static final categoryColumnCreatedAt = 'created_at';

  // make this a singleton class
  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  // only have a single app-wide reference to the database
  static Database? _database;
  Future<Database> get database async {
    if (_database != null) return _database!;
    // lazily instantiate the db the first time it is accessed
    _database = await _initDatabase();
    return _database!;
  }

  // this opens the database (and creates it if it doesn't exist)
  _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  // SQL code to create the database table
  Future _onCreate(Database db, int version) async {
    // 创建标记点表
    await db.execute('''
          CREATE TABLE $table (
            $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
            $columnTitle TEXT NOT NULL,
            $columnDescription TEXT,
            $columnLatitude REAL NOT NULL,
            $columnLongitude REAL NOT NULL,
            $columnCategory TEXT NOT NULL,
            $columnImagePaths TEXT,
            $columnAudioPath TEXT,
            $columnTags TEXT,
            $columnCreatedAt TEXT NOT NULL,
            $columnUpdatedAt TEXT,
            $columnIsPublic INTEGER NOT NULL DEFAULT 0,
            $columnShareId TEXT,
            $columnAddress TEXT,
            $columnRating INTEGER
          )
          ''');

    // 创建分类表
    await db.execute('''
          CREATE TABLE $categoryTable (
            $categoryColumnId INTEGER PRIMARY KEY AUTOINCREMENT,
            $categoryColumnName TEXT NOT NULL UNIQUE,
            $categoryColumnIcon TEXT NOT NULL,
            $categoryColumnColor INTEGER NOT NULL,
            $categoryColumnSortOrder INTEGER NOT NULL DEFAULT 0,
            $categoryColumnIsDefault INTEGER NOT NULL DEFAULT 0,
            $categoryColumnCreatedAt TEXT NOT NULL
          )
          ''');

    // 插入默认分类
    await _insertDefaultCategories(db);
  }

  // 数据库升级
  Future _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // 从版本1升级到版本2
      await db.execute('ALTER TABLE $table ADD COLUMN $columnImagePaths TEXT');
      await db.execute('ALTER TABLE $table ADD COLUMN $columnAudioPath TEXT');
      await db.execute('ALTER TABLE $table ADD COLUMN $columnTags TEXT');
      await db.execute('ALTER TABLE $table ADD COLUMN $columnUpdatedAt TEXT');
      await db.execute('ALTER TABLE $table ADD COLUMN $columnShareId TEXT');
      await db.execute('ALTER TABLE $table ADD COLUMN $columnAddress TEXT');
      await db.execute('ALTER TABLE $table ADD COLUMN $columnRating INTEGER');

      // 创建分类表
      await db.execute('''
            CREATE TABLE $categoryTable (
              $categoryColumnId INTEGER PRIMARY KEY AUTOINCREMENT,
              $categoryColumnName TEXT NOT NULL UNIQUE,
              $categoryColumnIcon TEXT NOT NULL,
              $categoryColumnColor INTEGER NOT NULL,
              $categoryColumnSortOrder INTEGER NOT NULL DEFAULT 0,
              $categoryColumnIsDefault INTEGER NOT NULL DEFAULT 0,
              $categoryColumnCreatedAt TEXT NOT NULL
            )
            ''');

      await _insertDefaultCategories(db);
    }
  }

  // 插入默认分类
  Future _insertDefaultCategories(Database db) async {
    final categories = CategoryModel.getDefaultCategories();
    for (final category in categories) {
      await db.insert(categoryTable, category.toMap());
    }
  }

  // Helper methods

  // Inserts a row in the database where each key in the Map is a column name
  // and the value is the column value. The return value is the id of the inserted row.
  Future<int> insert(Map<String, dynamic> row) async {
    Database db = await instance.database;
    return await db.insert(table, row);
  }

  // All of the rows are returned as a list of maps, where each map is 
  // a key-value list of columns.
  Future<List<Map<String, dynamic>>> queryAllRows() async {
    Database db = await instance.database;
    return await db.query(table);
  }

  // All of the methods (insert, query, update, delete) can also be done using
  // raw SQL commands. This method uses a raw query to give the row count.
  Future<int?> queryRowCount() async {
    Database db = await instance.database;
    return Sqflite.firstIntValue(await db.rawQuery('SELECT COUNT(*) FROM $table'));
  }

  // We are assuming here that the id column in the map is set. The other 
  // column values will be used to update the row.
  Future<int> update(Map<String, dynamic> row) async {
    Database db = await instance.database;
    int id = row[columnId];
    return await db.update(table, row, where: '$columnId = ?', whereArgs: [id]);
  }

  // Deletes the row specified by the id.
  Future<int> delete(int id) async {
    Database db = await instance.database;
    return await db.delete(table, where: '$columnId = ?', whereArgs: [id]);
  }

  // Query markers by category
  Future<List<Map<String, dynamic>>> queryByCategory(String category) async {
    Database db = await instance.database;
    return await db.query(table, where: '$columnCategory = ?', whereArgs: [category]);
  }

  // Query markers by search term in title or description
  Future<List<Map<String, dynamic>>> searchMarkers(String searchTerm) async {
    Database db = await instance.database;
    return await db.query(
      table,
      where: '$columnTitle LIKE ? OR $columnDescription LIKE ? OR $columnTags LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%']
    );
  }

  // 标记点相关方法 - 使用模型类
  Future<int> insertMarker(MarkerModel marker) async {
    Database db = await instance.database;
    return await db.insert(table, marker.toMap());
  }

  Future<List<MarkerModel>> getAllMarkers() async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(table, orderBy: '$columnCreatedAt DESC');
    return List.generate(maps.length, (i) => MarkerModel.fromMap(maps[i]));
  }

  Future<MarkerModel?> getMarkerById(int id) async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(
      table,
      where: '$columnId = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return MarkerModel.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateMarker(MarkerModel marker) async {
    Database db = await instance.database;
    return await db.update(
      table,
      marker.toMap(),
      where: '$columnId = ?',
      whereArgs: [marker.id],
    );
  }

  Future<int> deleteMarker(int id) async {
    Database db = await instance.database;
    return await db.delete(table, where: '$columnId = ?', whereArgs: [id]);
  }

  Future<List<MarkerModel>> getMarkersByCategory(String category) async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(
      table,
      where: '$columnCategory = ?',
      whereArgs: [category],
      orderBy: '$columnCreatedAt DESC',
    );
    return List.generate(maps.length, (i) => MarkerModel.fromMap(maps[i]));
  }

  Future<List<MarkerModel>> searchMarkersWithModel(String searchTerm) async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(
      table,
      where: '$columnTitle LIKE ? OR $columnDescription LIKE ? OR $columnTags LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: '$columnCreatedAt DESC',
    );
    return List.generate(maps.length, (i) => MarkerModel.fromMap(maps[i]));
  }

  // 分类相关方法
  Future<int> insertCategory(CategoryModel category) async {
    Database db = await instance.database;
    return await db.insert(categoryTable, category.toMap());
  }

  Future<List<CategoryModel>> getAllCategories() async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(
      categoryTable,
      orderBy: '$categoryColumnSortOrder ASC'
    );
    return List.generate(maps.length, (i) => CategoryModel.fromMap(maps[i]));
  }

  Future<int> updateCategory(CategoryModel category) async {
    Database db = await instance.database;
    return await db.update(
      categoryTable,
      category.toMap(),
      where: '$categoryColumnId = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteCategory(int id) async {
    Database db = await instance.database;
    return await db.delete(categoryTable, where: '$categoryColumnId = ?', whereArgs: [id]);
  }

  // 获取公开的标记点（用于分享）
  Future<List<MarkerModel>> getPublicMarkers() async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(
      table,
      where: '$columnIsPublic = ?',
      whereArgs: [1],
      orderBy: '$columnCreatedAt DESC',
    );
    return List.generate(maps.length, (i) => MarkerModel.fromMap(maps[i]));
  }

  // 根据分享ID获取标记点
  Future<MarkerModel?> getMarkerByShareId(String shareId) async {
    Database db = await instance.database;
    final List<Map<String, dynamic>> maps = await db.query(
      table,
      where: '$columnShareId = ?',
      whereArgs: [shareId],
    );
    if (maps.isNotEmpty) {
      return MarkerModel.fromMap(maps.first);
    }
    return null;
  }

  // 获取统计信息
  Future<Map<String, int>> getStatistics() async {
    Database db = await instance.database;

    final totalCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $table')
    ) ?? 0;

    final publicCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $table WHERE $columnIsPublic = 1')
    ) ?? 0;

    final categoryCounts = await db.rawQuery('''
      SELECT $columnCategory, COUNT(*) as count
      FROM $table
      GROUP BY $columnCategory
    ''');

    Map<String, int> categoryStats = {};
    for (var row in categoryCounts) {
      categoryStats[row['category'] as String] = row['count'] as int;
    }

    return {
      'total': totalCount,
      'public': publicCount,
      'private': totalCount - publicCount,
      ...categoryStats,
    };
  }
}