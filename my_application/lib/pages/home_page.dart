import 'package:flutter/material.dart';

class Post {
  final String username;
  final String avatarUrl;
  final String content;
  final List<String> mediaUrls; // Can be images or videos
  final int likes;
  final int comments;

  Post({
    required this.username,
    required this.avatarUrl,
    required this.content,
    required this.mediaUrls,
    required this.likes,
    required this.comments,
  });
}

class FeedPage extends StatefulWidget {
  const FeedPage({super.key});

  @override
  State<FeedPage> createState() => _FeedPageState();
}

class _FeedPageState extends State<FeedPage> {
  // Mock data for preview
  final List<Post> _posts = [
    Post(
      username: 'User1',
      avatarUrl: 'https://picsum.photos/150',
      content: 'This is a sample post with an image.',
      mediaUrls: ['https://picsum.photos/400'],
      likes: 100,
      comments: 20,
    ),
    Post(
      username: 'User2',
      avatarUrl: 'https://picsum.photos/150',
      content: 'Another post with multiple images.',
      mediaUrls: [
        'https://via.placeholder.com/400',
        'https://via.placeholder.com/401',
      ],
      likes: 150,
      comments: 30,
    ),
    <PERSON>(
      username: 'User3',
      avatarUrl: 'https://picsum.photos/150',
      content: 'A post with a video.',
      mediaUrls: ['https://flutter.github.io/assets-for-api-docs/assets/videos/bee.mp4'],
      likes: 200,
      comments: 50,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('动态'),
      ),
      body: GridView.builder(
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // Two columns for waterfall layout
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 0.7, // Adjust aspect ratio for card height
        ),
        itemCount: _posts.length,
        itemBuilder: (context, index) {
          return PostCard(post: _posts[index]);
        },
      ),
    );
  }
}

class PostCard extends StatelessWidget {
  final Post post;

  const PostCard({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info
          ListTile(
            leading: CircleAvatar(
              backgroundImage: NetworkImage(post.avatarUrl),
            ),
            title: Text(post.username),
          ),
          // Media (image or video)
          Expanded(
            child: post.mediaUrls.isNotEmpty
                ? Image.network(
              post.mediaUrls[0],
              fit: BoxFit.cover,
              width: double.infinity,
            )
                : const SizedBox.shrink(),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              post.content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Likes and comments
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                const Icon(Icons.favorite, size: 16),
                const SizedBox(width: 4),
                Text(post.likes.toString()),
                const SizedBox(width: 16),
                const Icon(Icons.comment, size: 16),
                const SizedBox(width: 4),
                Text(post.comments.toString()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
