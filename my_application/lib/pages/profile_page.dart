import 'package:flutter/material.dart';
import 'package:my_application/pages/login_page.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool isLoggedIn = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/profile_bg.jpg'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 32),
                  child: _buildUserProfileCard(),
                ),
                const Divider(height: 1, indent: 24, endIndent: 24, color: Colors.grey),
                _buildSettingsList(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserProfileCard() {
    return InkWell(
      onTap: () {
        if (!isLoggedIn) {
          Navigator.push(context, MaterialPageRoute(builder: (_) => const LoginPage()));
        }
      },
      child: Card(
        margin: const EdgeInsets.fromLTRB(16, 24, 16, 16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
          child: Column(
            children: [
              isLoggedIn
                  ? CircleAvatar(radius: 40)
                  : Icon(Icons.person, size: 80, color: Colors.grey),
              const SizedBox(height: 20),
              Text(
                isLoggedIn ? '旅行达人小美' : '请登录',
                style: TextStyle(fontSize: 20),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.star, size: 20, color: Colors.amber),
                  const SizedBox(width: 8),
                  Text(
                    '黄金会员 Lv.12',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsList() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: ListView(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          // 设置项列表（始终显示）
          ...[
            _buildListTile(Icons.edit, '编辑资料', () {
              if (!isLoggedIn) {
                Navigator.push(context, MaterialPageRoute(builder: (_) => const LoginPage()));
                return;
              }
              Navigator.push(context, MaterialPageRoute(builder: (_) => const EditProfilePage()));
            }),
            _buildListTile(Icons.security, '账号安全', () {
              if (!isLoggedIn) {
                Navigator.push(context, MaterialPageRoute(builder: (_) => const LoginPage()));
                return;
              }
              Navigator.push(context, MaterialPageRoute(builder: (_) => const AccountSecurityPage()));
            }),
            _buildListTile(Icons.notifications, '消息通知', () {
              if (!isLoggedIn) {
                Navigator.push(context, MaterialPageRoute(builder: (_) => const LoginPage()));
                return;
              }
              Navigator.push(context, MaterialPageRoute(builder: (_) => const NotificationSettingsPage()));
            }),
            _buildListTile(Icons.settings, '系统设置', () {
              if (!isLoggedIn) {
                Navigator.push(context, MaterialPageRoute(builder: (_) => const LoginPage()));
                return;
              }
              Navigator.push(context, MaterialPageRoute(builder: (_) => const SystemSettingsPage()));
            }),
            _buildListTile(
              Icons.help_outline,
              '帮助中心',
              () {
                if (!isLoggedIn) {
                  Navigator.push(context, MaterialPageRoute(builder: (_) => const LoginPage()));
                  return;
                }
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('帮助中心'),
                    content: const Text('客服联系方式：400-123-4567'),
                    actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('确定'))],
                  ),
                );
              },
            ),
            _buildListTile(
              isLoggedIn ? Icons.logout : Icons.login,
              isLoggedIn ? '退出登录' : '立即登录',
              () {
                if (isLoggedIn) {
                  setState(() => isLoggedIn = false);
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(builder: (_) => const LoginPage()),
                    (route) => false,
                  );
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const LoginPage()),
                  );
                }
              },
            ),
          ],
        ],
      ),
    );
  }

  ListTile _buildListTile(IconData icon, String title, Function onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue[800]),
      title: Text(title),
      trailing: Icon(Icons.chevron_right, color: Colors.grey[500]),
      onTap: () => onTap(),
    );
  }
}

// 设置项功能页面组件
class EditProfilePage extends StatelessWidget {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('编辑资料')),
      body: const Center(child: Text('用户资料编辑功能开发中')),
    );
  }
}

class AccountSecurityPage extends StatelessWidget {
  const AccountSecurityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('账号安全')),
      body: const Center(child: Text('双重认证、密码修改等功能开发中')),
    );
  }
}

class NotificationSettingsPage extends StatelessWidget {
  const NotificationSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('消息通知')),
      body: const Center(child: Text('推送通知设置功能开发中')),
    );
  }
}

class SystemSettingsPage extends StatelessWidget {
  const SystemSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('系统设置')),
      body: const Center(child: Text('主题、语言等系统设置功能开发中')),
    );
  }
}