<template>
	<view class="uni-container">
		<view v-if="!hasLeftWin" class="uni-header-logo">
			<image class="uni-header-image" src="/static/componentIndex.png"></image>
		</view>
		<view v-if="!hasLeftWin" class="uni-hello-text">
			<text class="hello-text">uni-app内置组件，展示样式仅供参考，文档详见：</text>
			<u-link class="hello-link" :href="'https://uniapp.dcloud.io/component/'" :text="'https://uniapp.dcloud.io/component/'"
			 :inWhiteList="true"></u-link>
		</view>
		<view class="uni-panel" v-for="(item, index) in list" :key="item.id">
			<view class="uni-panel-h" :class="item.open ? 'uni-panel-h-on' : ''" @click="triggerCollapse(index, item.id)">
				<text class="uni-panel-text">{{item.name}}</text>
				<text class="uni-panel-icon uni-icon" :class="item.open ? 'uni-panel-icon-on' : ''">{{item.pages ? '&#xe581;' : '&#xe470;'}}</text>
			</view>
			<view class="uni-panel-c" v-if="item.open">
				<view :class="{'left-win-active': leftWinActive === (item2.url ? item2.url.split('/')[3] : item2) && hasLeftWin}"
				 class="uni-navigate-item" v-for="(item2,key) in item.pages" :key="key" @click="goDetailPage(item.id, item2)">
					<text class="uni-navigate-text">{{item2.name ? item2.name : item2}}</text>
					<text class="uni-navigate-icon uni-icon">&#xe470;</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	// TODO 修复Android v3 加载过慢问题
	// #ifdef APP-PLUS
	var domModule = weex.requireModule('dom');
	domModule.addRule('fontFace', {
		'fontFamily': "uniicons",
		'src': "url('/static/uni.ttf')"
	});
	// #endif
	export default {
		props: {
			hasLeftWin: {
				type: Boolean
			},
			leftWinActive: {
				type: String
			}
		},
		data() {
			return {
				list: [{
						id: 'view',
						name: '视图容器',
						open: false,
						pages: [
							'view',
							'scroll-view',
							'swiper'
							// #ifndef MP-TOUTIAO || MP-LARK
							,
							'movable-view',
							'cover-view'
							// #endif
						]
					}, {
						id: 'content',
						name: '基础内容',
						open: false,
						pages: ['text', 'rich-text', 'progress']
					}, {
						id: 'form',
						name: '表单组件',
						open: false,
						pages: ['button', 'checkbox', 'form', 'input', 'label', 'picker', 'picker-view', 'radio',
							'slider',
							'switch', 'textarea',
							// #ifdef APP-PLUS || MP-WEIXIN || H5 || MP-BAIDU
							'editor',
							// #endif
						]
					}, {
						id: 'nav',
						name: '导航',
						open: false,
						pages: ['navigator']
					}, {
						id: 'media',
						name: '媒体组件',
						open: false,
						pages: [
							'image',
							'video'
						],
					},
					// #ifndef MP-TOUTIAO || MP-KUAISHOU
					{
						id: 'map',
						name: '地图',
						open: false,
						pages: ['map']

					},
					// #endif
					// #ifndef QUICKAPP-WEBVIEW-UNION
					{
						id: 'canvas',
						name: '画布',
						open: false,
						pages: ['canvas']
					},
					// #endif
					// #ifdef APP-PLUS || H5
					{
						id: 'web-view',
						name: '网页',
						open: false,
						pages: [{
							name: '网络网页',
							url: '/pages/component/web-view/web-view'
						}, {
							name: '本地网页',
							url: '/pages/component/web-view-local/web-view-local'
						}]
					},
					// #endif
					// #ifndef APP-PLUS || H5 || MP-LARK || MP-JD
					{
						id: 'web-view',
						name: '网页',
						open: false,
						pages: ['web-view']
					},
					// #endif
					// #ifndef H5 || MP-BAIDU || QUICKAPP-WEBVIEW || MP-LARK || MP-JD
					{
						id: 'ad',
						url: 'ad',
						name: 'AD组件',
						open: false
					},
					// #endif
				]
			}
		},
		onShareAppMessage() {
			return {
				title: '欢迎体验uni-app',
				path: '/pages/tabBar/component/component'
			}
		},
		onNavigationBarButtonTap(e) {
			uni.navigateTo({
				url: '/pages/about/about'
			});
		},
		// #ifdef H5
		watch: {
			$route: {
				immediate: true,
				handler(newRoute) {
					if (newRoute.matched.length) {
						let path = newRoute.path.split('/')[3]
						for (const item of this.list) {
							if (Array.isArray(item.pages)) {
								for (const page of item.pages) {
									if (page === path || page.url && page.url === newRoute.path) {
										item.open = true
									}
								}
							}
						}
					}
				}
			}
		},
		// #endif
		methods: {
			triggerCollapse(e, id) {
				if (!this.list[e].pages) {
					this.goDetailPage('', this.list[e].url);
					return;
				}
				for (var i = 0; i < this.list.length; ++i) {
					if (e === i) {
						this.list[i].open = !this.list[i].open;
					} else {
						this.list[i].open = false;
					}
				}
			},
			goDetailPage(panel, e) {
				if (typeof e === 'string') {
					const url = '/pages/component/' + e + '/' + e
					if (this.hasLeftWin) {
						uni.reLaunch({
							url: url
						})
					} else {
						uni.navigateTo({
							url: url
						})
					}
				} else {
					if (this.hasLeftWin) {
						uni.reLaunch({
							url: e.url
						})
					} else {
						uni.navigateTo({
							url: e.url
						})
					}
				}
			}
		}
	}
</script>

<style>
	@import '../../../common/uni-nvue.css';
</style>
