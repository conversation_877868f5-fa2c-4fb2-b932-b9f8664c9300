<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">列表组件可以在其中使用图标、略缩图或放置任何你想放的元素，使用场景如：导航菜单、列表、设置中心排版等</text>
		</uni-card>
		<uni-section title="基础用法" type="line">
			<uni-list>
				<uni-list-item title="列表文字" />
				<uni-list-item title="列表文字" rightText="右侧文字" />
				<uni-list-item title="列表文字" note="列表描述信息" rightText="右侧文字" />
			</uni-list>
		</uni-section>
		<uni-section title="禁用列表" type="line">
			<uni-list>
				<uni-list-item :disabled="true" title="列表禁用状态" rightText="右侧文字" />
				<uni-list-item :disabled="true" title="列表禁用状态" rightText="右侧文字" />
			</uni-list>
		</uni-section>
		<uni-section title="显示右侧箭头" type="line">
			<uni-list>
				<uni-list-item showArrow title="列表文字" />
				<uni-list-item showArrow title="列表文字" rightText="右侧文字" />
			</uni-list>
		</uni-section>
		<uni-section title="开启点击反馈" type="line">
			<uni-list>
				<uni-list-item title="弹窗提示" clickable @click="onClick" />
				<uni-list-item title="页面跳转" :to="`./chat`" @click="onClick" />
				<uni-list-item title="关闭当前页面打开新页面" showArrow link="redirectTo" to="./chat" @click="onClick" />
				<uni-list-item title="打开错误页面(查看控制台)" showArrow link="redirectTo" to="./chats" @click="onClick" />
			</uni-list>
		</uni-section>

		<uni-section title="不显示分隔线" type="line">
			<uni-list :border="false">
				<uni-list-item title="列表文字" />
				<uni-list-item :border="false" title="列表文字" note="列表描述信息" rightText="右侧文字" />
				<uni-list-item :border="false" title="列表文字" note="列表描述信息" rightText="右侧文字" />
			</uni-list>
		</uni-section>

		<uni-section title="分隔线通栏" type="line">
			<uni-list border-full>
				<uni-list-item title="列表文字" />
				<uni-list-item title="列表文字" note="列表描述信息" rightText="右侧文字" />
				<uni-list-item title="列表文字" note="列表描述信息" rightText="右侧文字" />
			</uni-list>
		</uni-section>

		<uni-section title="文字溢出隐藏" type="line">
			<uni-list>
				<uni-list-item ellipsis="1" title="超长文字显示一行,以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字" />
				<uni-list-item ellipsis="2" title="超长文字显示二行,以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字"
					rightText="二行显示" />
				<uni-list-item ellipsis="1" title="全部显示,以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字"
					note="列表描述信息,下是测试文字,下是测试文字,下是测试文字,下是测试文字,下是测试文字,下是测试文字,下是测试文字" showArrow rightText="一行显示" />
				<uni-list-item title="全部显示,以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字以下是测试文字" showArrow rightText="全部" />
			</uni-list>
		</uni-section>

		<uni-section title="显示图标或图片" type="line">
			<uni-list>
				<uni-list-item :show-extra-icon="true" showArrow :extra-icon="extraIcon" title="列表左侧带扩展图标" />
				<uni-list-item title="列表左侧带略缩图" note="列表描述信息" showArrow
					thumb="/static/c1.png"
					thumb-size="sm" rightText="小图" />

				<uni-list-item title="列表左侧带略缩图" note="列表描述信息" showArrow
					thumb="https://web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png"
					thumb-size="base" rightText="默认" />
				<uni-list-item title="列表左侧带略缩图" note="列表描述信息" showArrow
					thumb="https://web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png"
					thumb-size="lg" rightText="大图" />
			</uni-list>
		</uni-section>

		<uni-section title="使用插槽" type="line">
			<uni-list>
				<uni-list-item>
					<template v-slot:body>
						<view class="slot-box">
							<text class="slot-text">使用 body 插槽</text>
						</view>
					</template>
				</uni-list-item>
				<uni-list-item title="自定义右侧插槽" note="列表描述信息" link>
					<template v-slot:footer>
						<image class="slot-image" src="/static/logo.png" mode="widthFix"></image>
					</template>
				</uni-list-item>
				<uni-list-item>
					<template v-slot:header>
						<view class="slot-box">
							<image class="slot-image" src="/static/logo.png" mode="widthFix"></image>
						</view>
					</template>
					<template v-slot:body>
						<text class="slot-box slot-text">自定义两侧插槽</text>
					</template>
					<template v-slot:footer>
						<image class="slot-image" src="/static/logo.png" mode="widthFix"></image>
					</template>
				</uni-list-item>
			</uni-list>
		</uni-section>
	</view>
</template>

<script>
	export default {
		components: {},
		data() {
			return {
				cover: 'https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao.jpg',
				avatar: 'https://web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png',
				extraIcon: {
					color: '#4cd964',
					size: '22',
					type: 'gear-filled'
				}
			};
		},
		methods: {
			onClick(e) {
				console.log('执行click事件', e.data)
				uni.showToast({
					title: '点击反馈'
				});
			},
			switchChange(e) {
				uni.showToast({
					title: 'change:' + e.value,
					icon: 'none'
				});
			}
		}
	};
</script>

<style lang="scss">
	.slot-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
	}

	.slot-image {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		margin-right: 10px;
		width: 30px;
		height: 30px;
	}

	.slot-text {
		flex: 1;
		font-size: 14px;
		color: #4cd964;
		margin-right: 10px;
	}
</style>
