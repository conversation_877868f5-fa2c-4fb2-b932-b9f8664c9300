{"id": "uts-progressNotification", "displayName": "uts-progressNotification", "version": "1.1.0", "description": "uts-progressNotification", "keywords": ["uts-progressNotification"], "repository": "", "engines": {"HBuilderX": "^3.91"}, "dcloudext": {"type": "uts", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "TargetSDKVersion33以上时需配置\n`android.permission.POST_NOTIFICATIONS`"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": {"minVersion": "19"}, "app-ios": "n"}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}