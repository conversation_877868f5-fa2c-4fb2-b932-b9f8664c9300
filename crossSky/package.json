{"id": "hello-uniapp", "name": "hello-uniapp", "displayName": "hello-uniapp 示例工程", "version": "3.4.7", "description": "uni-app 框架示例，一套代码，同时发行到iOS、Android、H5、小程序等多个平台，请使用手机扫码快速体验 uni-app 的强大功能", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": "https://github.com/dcloudio/hello-uniapp.git", "keywords": ["hello-uniapp", "uni-app", "uni-ui", "示例工程"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/dcloudio/hello-uniapp/issues"}, "homepage": "https://github.com/dcloudio/hello-uniapp#readme", "dependencies": {}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "uniapp-template-project"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "y", "app-nvue": "y", "app-harmony": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "京东": "y", "钉钉": "y", "快手": "y", "飞书": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}, "hello-uniapp-demo": {"title": "hello-uniapp 演示网站", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-DEMO": true}}}}}