package main

import (
	"os"
	"net/http"

	"github.com/chagee/go-backend/internal/config"
	"github.com/chagee/go-backend/internal/routes"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

func init() {
	// 创建日志目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Fatalf("创建日志目录失败: %v", err)
	}

	// 加载配置
	if err := config.LoadConfig(); err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 获取配置
	cfg := config.Get()

	// 配置logrus日志输出到文件
	log.SetOutput(&lumberjack.Logger{
		Filename:   cfg.Log.Filename,
		MaxSize:    cfg.Log.MaxSize,
		MaxBackups: cfg.Log.MaxBackups,
		MaxAge:     cfg.Log.MaxAge,
		Compress:   cfg.Log.Compress,
	})

	// 设置日志格式
	if cfg.Log.Formatter == "json" {
		log.SetFormatter(&log.JSONFormatter{
			TimestampFormat: cfg.Log.TimestampFormat,
		})
	} else {
		log.SetFormatter(&log.TextFormatter{
			TimestampFormat: cfg.Log.TimestampFormat,
			FullTimestamp:   true,
		})
	}

	// 设置日志级别
	level, err := log.ParseLevel(cfg.Log.Level)
	if err != nil {
		log.Warnf("无效的日志级别: %s, 使用默认级别: InfoLevel", cfg.Log.Level)
		level = log.InfoLevel
	}
	log.SetLevel(level)

	log.Info("Logrus日志系统初始化成功")

	// 初始化数据库
	if err := config.InitDB(cfg.DB); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
}

func main() {
	// 初始化Gin引擎
	router := gin.Default()

	// 注册API路由
	routes.SetupAPIRoutes(router)

	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// 启动服务器 - 直接设置端口号以避免格式字符串错误
	log.Printf("服务器启动在端口 8082")
	if err := router.Run(":8082"); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
