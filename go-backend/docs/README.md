# 项目结构说明

## 项目概述
本项目是一个基于Go语言的后端服务，采用分层架构设计，包含配置管理、路由注册、数据库操作等核心功能模块。

## 目录结构
```
go-backend/
├── api/                  # API接口定义
├── cmd/
│   └── api/              # 应用入口点
├── configs/              # 配置文件目录
│   └── config.yaml       # 主配置文件
├── docs/                 # 项目文档
├── internal/             # 内部代码模块
│   ├── app/              # 应用初始化
│   ├── config/           # 配置管理
│   ├── handler/          # 请求处理器
│   ├── model/            # 数据模型
│   ├── routes/           # 路由定义
│   └── service/          # 业务逻辑
├── pkg/                  # 公共包
│   └── utils/            # 工具函数
├── scripts/              # 脚本文件
└── test/                 # 测试代码
```

## 核心模块说明
1. **配置管理**
   - 位置: `internal/config/`
   - 功能: 加载和解析配置文件，提供全局配置访问
   - 配置文件: `configs/config.yaml`

2. **路由系统**
   - 位置: `internal/routes/`
   - 功能: 定义API路由和处理器映射
   - 主要文件: `user_routes.go` (用户相关路由)

3. **数据库操作**
   - 位置: `internal/model/`
   - 功能: 数据模型定义和数据库交互

4. **业务逻辑**
   - 位置: `internal/service/`
   - 功能: 实现核心业务逻辑

5. **API处理器**
   - 位置: `internal/handler/`
   - 功能: 处理HTTP请求和响应

## 配置说明
配置文件 `configs/config.yaml` 包含以下主要配置项：
- `server`: 服务器配置（端口等）
- `database`: 数据库连接配置
- `routes`: 路由前缀和路径配置

## 运行说明
1. 确保配置文件正确设置
2. 执行命令: `go run cmd/api/main.go`