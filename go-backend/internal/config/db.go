package config

import (
	"fmt"

	"github.com/chagee/go-backend/internal/model"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB 初始化数据库连接
func InitDB(config DBConfig) error {
	var err error
	DB, err = gorm.Open(mysql.Open(config.DSN), &gorm.Config{Logger: logger.Default.LogMode(logger.Info)})
	if err != nil {
		return fmt.Errorf("数据库连接失败: %v", err)
	}

	// 自动迁移数据表
	if err := DB.AutoMigrate(
		&model.User{},
		&model.District{},
		&model.Post{},
		&model.TeamActivity{},
		&model.ActivityParticipant{},
		&model.Comment{},
	); err != nil {
		return fmt.Errorf("数据表迁移失败: %v", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接池失败: %v", err)
	}
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)

	return nil
}