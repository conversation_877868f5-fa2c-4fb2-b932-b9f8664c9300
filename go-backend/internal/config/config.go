package config

import (
	"fmt"
	"github.com/spf13/viper"
)

type ServerConfig struct {
	Port int `mapstructure:"port"`
}

type DBConfig struct {
	DSN          string `mapstructure:"dsn"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
}

type RouteConfig struct {
	ApiPrefix string `mapstructure:"api_prefix"`
	Users     struct {
		BasePath  string `mapstructure:"base"`
		Create    string `mapstructure:"create"`
		GetByID   string `mapstructure:"get_by_id"`
	}
}

// LogConfig 日志配置
type LogConfig struct {
	Filename        string `mapstructure:"filename"`
	MaxSize         int    `mapstructure:"max_size"`
	MaxBackups      int    `mapstructure:"max_backups"`
	MaxAge          int    `mapstructure:"max_age"`
	Compress        bool   `mapstructure:"compress"`
	Formatter       string `mapstructure:"formatter"`
	Level           string `mapstructure:"level"`
	TimestampFormat string `mapstructure:"timestamp_format"`
}

// HTTPConfig HTTP服务器配置
type HTTPConfig struct {
	ReadTimeout  int `mapstructure:"read_timeout"`
	WriteTimeout int `mapstructure:"write_timeout"`
	IdleTimeout  int `mapstructure:"idle_timeout"`
}

// Config 应用配置
type Config struct {
	Server ServerConfig `mapstructure:"server"`
	Routes RouteConfig  `mapstructure:"routes"`
	DB     DBConfig     `mapstructure:"database"`
	Log    LogConfig    `mapstructure:"log"`
	HTTP   HTTPConfig   `mapstructure:"http"`
}

var cfg Config

func LoadConfig() error {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %v", err)
	}

	if err := viper.Unmarshal(&cfg); err != nil {
		return fmt.Errorf("failed to unmarshal config: %v", err)
	}

	return nil
}

func Get() Config {
	return cfg
}