package service

import (
	"context"

	"github.com/chagee/go-backend/internal/model"
	"gorm.io/gorm"
)

// CommentService 评论服务接口
type CommentService interface {
	CreateComment(ctx context.Context, comment *model.Comment) error
	GetCommentByID(ctx context.Context, id uint) (*model.Comment, error)
	UpdateComment(ctx context.Context, comment *model.Comment) error
	DeleteComment(ctx context.Context, id uint) error
	ListCommentsByPostID(ctx context.Context, postID uint, page, pageSize int) ([]*model.Comment, int64, error)
}

// commentService 评论服务实现
type commentService struct {
	db *gorm.DB
}

// NewCommentService 创建评论服务实例
func NewCommentService(db *gorm.DB) CommentService {
	return &commentService{db: db}
}

// CreateComment 创建评论
func (s *commentService) CreateComment(ctx context.Context, comment *model.Comment) error {
	return s.db.WithContext(ctx).Create(comment).Error
}

// GetCommentByID 根据ID获取评论
func (s *commentService) GetCommentByID(ctx context.Context, id uint) (*model.Comment, error) {
	var comment model.Comment
	result := s.db.WithContext(ctx).First(&comment, id)
	return &comment, result.Error
}

// UpdateComment 更新评论
func (s *commentService) UpdateComment(ctx context.Context, comment *model.Comment) error {
	return s.db.WithContext(ctx).Save(comment).Error
}

// DeleteComment 删除评论
func (s *commentService) DeleteComment(ctx context.Context, id uint) error {
	return s.db.WithContext(ctx).Delete(&model.Comment{}, id).Error
}

// ListCommentsByPostID 根据帖子ID分页获取评论
func (s *commentService) ListCommentsByPostID(ctx context.Context, postID uint, page, pageSize int) ([]*model.Comment, int64, error) {
	var comments []*model.Comment
	var count int64

	// 获取总数
	if err := s.db.WithContext(ctx).Model(&model.Comment{}).Where("post_id = ?", postID).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := s.db.WithContext(ctx).Where("post_id = ?", postID).Offset(offset).Limit(pageSize).Find(&comments).Error; err != nil {
		return nil, 0, err
	}

	return comments, count, nil
}