package service

import (
	"context"
	"errors"

	"github.com/chagee/go-backend/internal/model"
	"gorm.io/gorm"
)

// TeamActivityService 组队活动服务接口
type TeamActivityService interface {
	CreateTeamActivity(ctx context.Context, activity *model.TeamActivity) error
	GetTeamActivityByID(ctx context.Context, id uint) (*model.TeamActivity, error)
	UpdateTeamActivity(ctx context.Context, activity *model.TeamActivity) error
	DeleteTeamActivity(ctx context.Context, id uint) error
	ListTeamActivities(ctx context.Context, page, pageSize int) ([]*model.TeamActivity, int64, error)
	JoinActivity(ctx context.Context, participant *model.ActivityParticipant) error
	LeaveActivity(ctx context.Context, activityID, userID uint) error
	GetActivityParticipants(ctx context.Context, activityID uint) ([]*model.ActivityParticipant, error)
}

// teamActivityService 组队活动服务实现
type teamActivityService struct {
	db *gorm.DB
}

// NewTeamActivityService 创建组队活动服务实例
func NewTeamActivityService(db *gorm.DB) TeamActivityService {
	return &teamActivityService{db: db}
}

// CreateTeamActivity 创建组队活动
func (s *teamActivityService) CreateTeamActivity(ctx context.Context, activity *model.TeamActivity) error {
	return s.db.WithContext(ctx).Create(activity).Error
}

// GetTeamActivityByID 根据ID获取组队活动
func (s *teamActivityService) GetTeamActivityByID(ctx context.Context, id uint) (*model.TeamActivity, error) {
	var activity model.TeamActivity
	result := s.db.WithContext(ctx).First(&activity, id)
	return &activity, result.Error
}

// UpdateTeamActivity 更新组队活动
func (s *teamActivityService) UpdateTeamActivity(ctx context.Context, activity *model.TeamActivity) error {
	return s.db.WithContext(ctx).Save(activity).Error
}

// DeleteTeamActivity 删除组队活动
func (s *teamActivityService) DeleteTeamActivity(ctx context.Context, id uint) error {
	return s.db.WithContext(ctx).Delete(&model.TeamActivity{}, id).Error
}

// ListTeamActivities 分页获取组队活动列表
func (s *teamActivityService) ListTeamActivities(ctx context.Context, page, pageSize int) ([]*model.TeamActivity, int64, error) {
	var activities []*model.TeamActivity
	var count int64

	if err := s.db.WithContext(ctx).Model(&model.TeamActivity{}).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := s.db.WithContext(ctx).Offset(offset).Limit(pageSize).Find(&activities).Error; err != nil {
		return nil, 0, err
	}

	return activities, count, nil
}

// JoinActivity 加入组队活动
func (s *teamActivityService) JoinActivity(ctx context.Context, participant *model.ActivityParticipant) error {
	// 检查活动是否存在
	var activity model.TeamActivity
	if err := s.db.WithContext(ctx).First(&activity, participant.TeamActivityID).Error; err != nil {
		return errors.New("活动不存在")
	}

	// 检查活动状态
	if activity.Status != "recruiting" {
		return errors.New("活动不处于招募状态")
	}

	// 检查是否已达到人数上限
	var participantCount int64
	s.db.WithContext(ctx).Model(&model.ActivityParticipant{}).
		Where("team_activity_id = ? AND status = ?", participant.TeamActivityID, "joined").
		Count(&participantCount)

	if participantCount >= int64(activity.MaxParticipants) {
		return errors.New("活动人数已达上限")
	}

	// 检查是否已加入
	var existingParticipant model.ActivityParticipant
	result := s.db.WithContext(ctx).Where(
		"team_activity_id = ? AND user_id = ?",
		participant.TeamActivityID,
		participant.UserID,
	).First(&existingParticipant)

	if result.Error == nil {
		if existingParticipant.Status == "joined" {
			return errors.New("已加入该活动")
		} else {
			// 更新状态为已加入
			existingParticipant.Status = "joined"
			existingParticipant.JoinedAt = participant.JoinedAt
			return s.db.WithContext(ctx).Save(&existingParticipant).Error
		}
	} else if result.Error != gorm.ErrRecordNotFound {
		return result.Error
	}

	// 创建新的参与者记录
	return s.db.WithContext(ctx).Create(participant).Error
}

// LeaveActivity 退出组队活动
func (s *teamActivityService) LeaveActivity(ctx context.Context, activityID, userID uint) error {
	return s.db.WithContext(ctx).
		Where("team_activity_id = ? AND user_id = ?", activityID, userID).
		Update("status", "cancelled").Error
}

// GetActivityParticipants 获取活动参与者列表
func (s *teamActivityService) GetActivityParticipants(ctx context.Context, activityID uint) ([]*model.ActivityParticipant, error) {
	var participants []*model.ActivityParticipant
	err := s.db.WithContext(ctx).
		Where("team_activity_id = ? AND status = ?", activityID, "joined").
		Find(&participants).Error
	return participants, err
}