package service

import (
	"github.com/chagee/go-backend/internal/config"
	"github.com/chagee/go-backend/internal/model"
)

// DistrictService 地区服务接口
type DistrictService interface {
	CreateDistrict(district *model.District) error
	GetDistrictByAdcode(adcode string) (*model.District, error)
	ListDistricts() ([]*model.District, error)
	ListDistrictsByParent(parentAdcode string) ([]*model.District, error)
	UpdateDistrict(district *model.District) error
	DeleteDistrict(adcode string) error
}

// districtService 实现DistrictService接口
type districtService struct{}

// NewDistrictService 创建地区服务实例
func NewDistrictService() DistrictService {
	return &districtService{}
}

// CreateDistrict 创建地区
func (s *districtService) CreateDistrict(district *model.District) error {
	return config.DB.Create(district).Error
}

// GetDistrictByAdcode 根据adcode获取地区
func (s *districtService) GetDistrictByAdcode(adcode string) (*model.District, error) {
	var district model.District
	result := config.DB.Where("adcode = ?", adcode).First(&district)
	return &district, result.Error
}

// ListDistricts 获取所有地区
func (s *districtService) ListDistricts() ([]*model.District, error) {
	var districts []*model.District
	result := config.DB.Find(&districts)
	return districts, result.Error
}

// ListDistrictsByParent 根据父级adcode获取子地区
func (s *districtService) ListDistrictsByParent(parentAdcode string) ([]*model.District, error) {
	var districts []*model.District
	result := config.DB.Where("parent_adcode = ?", parentAdcode).Find(&districts)
	return districts, result.Error
}

// UpdateDistrict 更新地区
func (s *districtService) UpdateDistrict(district *model.District) error {
	return config.DB.Save(district).Error
}

// DeleteDistrict 删除地区
func (s *districtService) DeleteDistrict(adcode string) error {
	return config.DB.Where("adcode = ?", adcode).Delete(&model.District{}).Error
}