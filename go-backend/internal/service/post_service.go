package service

import (
	"context"

	"github.com/chagee/go-backend/internal/model"
	"gorm.io/gorm"
)

// PostService 帖子服务接口
type PostService interface {
	CreatePost(ctx context.Context, post *model.Post) error
	GetPostByID(ctx context.Context, id uint) (*model.Post, error)
	UpdatePost(ctx context.Context, post *model.Post) error
	DeletePost(ctx context.Context, id uint) error
	ListPosts(ctx context.Context, page, pageSize int) ([]*model.Post, int64, error)
}

// postService 帖子服务实现
type postService struct {
	db *gorm.DB
}

// NewPostService 创建帖子服务实例
func NewPostService(db *gorm.DB) PostService {
	return &postService{db: db}
}

// CreatePost 创建帖子
func (s *postService) CreatePost(ctx context.Context, post *model.Post) error {
	return s.db.WithContext(ctx).Create(post).Error
}

// GetPostByID 根据ID获取帖子
func (s *postService) GetPostByID(ctx context.Context, id uint) (*model.Post, error) {
	var post model.Post
	result := s.db.WithContext(ctx).First(&post, id)
	return &post, result.Error
}

// UpdatePost 更新帖子
func (s *postService) UpdatePost(ctx context.Context, post *model.Post) error {
	return s.db.WithContext(ctx).Save(post).Error
}

// DeletePost 删除帖子
func (s *postService) DeletePost(ctx context.Context, id uint) error {
	return s.db.WithContext(ctx).Delete(&model.Post{}, id).Error
}

// ListPosts 分页获取帖子列表
func (s *postService) ListPosts(ctx context.Context, page, pageSize int) ([]*model.Post, int64, error) {
	var posts []*model.Post
	var count int64

	// 获取总数
	if err := s.db.WithContext(ctx).Model(&model.Post{}).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := s.db.WithContext(ctx).Offset(offset).Limit(pageSize).Find(&posts).Error; err != nil {
		return nil, 0, err
	}

	return posts, count, nil
}