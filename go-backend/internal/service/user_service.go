package service

import (
	"github.com/chagee/go-backend/internal/config"
	"github.com/chagee/go-backend/internal/model"
)

// UserService 用户服务接口
type UserService interface {
	CreateUser(user *model.User) error
	GetUserByID(id uint) (*model.User, error)
	GetUserByUsername(username string) (*model.User, error)
	UpdateUser(user *model.User) error
	DeleteUser(id uint) error
	ListUsers() ([]*model.User, error)
}

// userService 实现UserService接口
type userService struct{}

// NewUserService 创建用户服务实例
func NewUserService() UserService {
	return &userService{}
}

// CreateUser 创建新用户
func (s *userService) CreateUser(user *model.User) error {
	return config.DB.Create(user).Error
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(id uint) (*model.User, error) {
	var user model.User
	result := config.DB.First(&user, id)
	return &user, result.Error
}

// GetUserByUsername 根据用户名获取用户
func (s *userService) GetUserByUsername(username string) (*model.User, error) {
	var user model.User
	result := config.DB.Where("username = ?", username).First(&user)
	return &user, result.Error
}

// UpdateUser 更新用户信息
func (s *userService) UpdateUser(user *model.User) error {
	return config.DB.Save(user).Error
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(id uint) error {
	return config.DB.Delete(&model.User{}, id).Error
}

// ListUsers 获取所有用户
func (s *userService) ListUsers() ([]*model.User, error) {
	var users []*model.User
	result := config.DB.Find(&users)
	return users, result.Error
}