package model

import (
	"time"

	"gorm.io/gorm"
)

// ActivityParticipant 活动参与者模型
type ActivityParticipant struct {
	ID              uint           `gorm:"primarykey" json:"id"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	TeamActivityID  uint           `gorm:"not null;index;comment:组队活动ID" json:"team_activity_id"`
	UserID          uint           `gorm:"not null;index;comment:参与者用户ID" json:"user_id"`
	Status          string         `gorm:"size:20;default:'joined';index" json:"status"` // joined, cancelled, rejected
	JoinedAt        time.Time      `gorm:"default:CURRENT_TIMESTAMP(3)" json:"joined_at"`

	// 联合唯一索引：一个用户只能参与一个活动一次
	_               []byte         `gorm:"uniqueIndex:idx_activity_user,comment:用户-活动唯一索引"`
}

// TableName 设置表名
func (ActivityParticipant) TableName() string {
	return "activity_participants"
}