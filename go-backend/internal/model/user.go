package model

import (
	"time"

	"gorm.io/gorm"
)

// User 定义用户模型
type User struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	Username  string         `gorm:"size:50;uniqueIndex" json:"username"`
	Email     string         `gorm:"size:100;uniqueIndex" json:"email"`
	Password  string         `gorm:"size:100" json:"-"` // 密码不返回给前端
	Age       *int           `gorm:"default:null" json:"age,omitempty"`
}

// TableName 设置表名
func (User) TableName() string {
	return "users"
}