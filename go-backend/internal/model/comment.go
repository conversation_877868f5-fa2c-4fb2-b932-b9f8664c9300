package model

import (
	"time"

	"gorm.io/gorm"
)

// Comment 帖子评论模型
type Comment struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	PostID    uint           `gorm:"not null;index;comment:关联的帖子ID" json:"post_id"`
	UserID    uint           `gorm:"not null;index;comment:评论用户ID" json:"user_id"`
	Content   string         `gorm:"type:text;not null" json:"content"`
	ParentID  *uint          `gorm:"index;null;comment:父评论ID，用于嵌套评论" json:"parent_id,omitempty"`
}

// TableName 设置表名
func (Comment) TableName() string {
	return "comments"
}