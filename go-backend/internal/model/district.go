package model

import (
	"time"

	"gorm.io/gorm"
)

// District 地区模型
type District struct {
	ID           uint           `gorm:"primarykey" json:"id"`
	Adcode       string         `gorm:"type:varchar(20);not null" json:"adcode"` // 临时移除uniqueIndex
	Name         string         `gorm:"type:varchar(100);not null" json:"name"`
	Center       *string        `gorm:"type:varchar(50);null" json:"center,omitempty"`
	Level        *string        `gorm:"type:varchar(20);null" json:"level,omitempty"`
	ParentAdcode *string        `gorm:"type:varchar(20);null;index" json:"parent_adcode,omitempty"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 设置表名为district
func (District) TableName() string {
	return "district"
}
