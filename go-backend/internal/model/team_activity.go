package model

import (
	"time"

	"gorm.io/gorm"
)

// TeamActivity 组队活动模型
type TeamActivity struct {
	ID             uint           `gorm:"primarykey" json:"id"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	PostID         uint           `gorm:"not null;uniqueIndex;comment:关联的帖子ID" json:"post_id"`
	Title          string         `gorm:"size:200;not null" json:"title"`
	Description    string         `gorm:"type:text" json:"description,omitempty"`
	ActivityTime   time.Time      `gorm:"not null" json:"activity_time"`
	Location       string         `gorm:"size:200;not null" json:"location"`
	Cost           float64        `gorm:"default:0" json:"cost,omitempty"`
	MaxParticipants int           `gorm:"not null;default:10" json:"max_participants"`
	Status         string         `gorm:"size:20;default:'recruiting';index" json:"status"` // recruiting, ongoing, completed, cancelled
	CreatorID      uint           `gorm:"not null;index" json:"creator_id"`
}

// TableName 设置表名
func (TeamActivity) TableName() string {
	return "team_activities"
}