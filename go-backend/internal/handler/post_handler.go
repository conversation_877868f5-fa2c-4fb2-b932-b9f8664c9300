package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/model"
	"github.com/chagee/go-backend/internal/service"
)

// PostHandler 帖子API处理器
type PostHandler struct {
	postService service.PostService
}

// NewPostHandler 创建帖子API处理器实例
func NewPostHandler(postService service.PostService) *PostHandler {
	return &PostHandler{postService: postService}
}

// CreatePost 创建帖子
// @Summary 创建新帖子
// @Description 创建新的论坛帖子
// @Tags posts
// @Accept json
// @Produce json
// @Param post body model.Post true "帖子信息"
// @Success 201 {object} model.Post
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/posts [post]
func (h *PostHandler) CreatePost(c *gin.Context) {
	var post model.Post
	if err := c.ShouldBindJSON(&post); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	if err := h.postService.CreatePost(c.Request.Context(), &post); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建帖子失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, post)
}

// GetPost 获取帖子详情
// @Summary 获取帖子详情
// @Description 根据ID获取帖子详细信息
// @Tags posts
// @Accept json
// @Produce json
// @Param id path int true "帖子ID"
// @Success 200 {object} model.Post
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/posts/{id} [get]
func (h *PostHandler) GetPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的帖子ID"})
		return
	}

	post, err := h.postService.GetPostByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取帖子失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, post)
}

// UpdatePost 更新帖子
// @Summary 更新帖子
// @Description 根据ID更新帖子信息
// @Tags posts
// @Accept json
// @Produce json
// @Param id path int true "帖子ID"
// @Param post body model.Post true "更新的帖子信息"
// @Success 200 {object} model.Post
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/posts/{id} [put]
func (h *PostHandler) UpdatePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的帖子ID"})
		return
	}

	var post model.Post
	if err := c.ShouldBindJSON(&post); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}
	post.ID = uint(id)

	if err := h.postService.UpdatePost(c.Request.Context(), &post); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新帖子失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, post)
}

// DeletePost 删除帖子
// @Summary 删除帖子
// @Description 根据ID删除帖子
// @Tags posts
// @Accept json
// @Produce json
// @Param id path int true "帖子ID"
// @Success 204 {object} nil
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/posts/{id} [delete]
func (h *PostHandler) DeletePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的帖子ID"})
		return
	}

	if err := h.postService.DeletePost(c.Request.Context(), uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除帖子失败: " + err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListPosts 列出帖子
// @Summary 分页列出帖子
// @Description 分页获取帖子列表
// @Tags posts
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页条数，默认10"
// @Success 200 {object} map[string]interface{}{"data":[]model.Post, "total":int64, "page":int, "pageSize":int}
// @Failure 500 {object} map[string]string
// @Router /api/posts [get]
func (h *PostHandler) ListPosts(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	posts, total, err := h.postService.ListPosts(c.Request.Context(), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取帖子列表失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":     posts,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}