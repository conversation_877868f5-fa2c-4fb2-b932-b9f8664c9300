package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/model"
	"github.com/chagee/go-backend/internal/service"
)

// TeamActivityHandler 组队活动API处理器
type TeamActivityHandler struct {
	teamActivityService service.TeamActivityService
}

// NewTeamActivityHandler 创建组队活动API处理器实例
func NewTeamActivityHandler(teamActivityService service.TeamActivityService) *TeamActivityHandler {
	return &TeamActivityHandler{teamActivityService: teamActivityService}
}

// CreateTeamActivity 创建组队活动
// @Summary 创建新的组队活动
// @Description 创建新的组队活动
// @Tags team-activities
// @Accept json
// @Produce json
// @Param activity body model.TeamActivity true "组队活动信息"
// @Success 201 {object} model.TeamActivity
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities [post]
func (h *TeamActivityHandler) CreateTeamActivity(c *gin.Context) {
	var activity model.TeamActivity
	if err := c.ShouldBindJSON(&activity); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	if err := h.teamActivityService.CreateTeamActivity(c.Request.Context(), &activity); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建组队活动失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, activity)
}

// GetTeamActivity 获取组队活动详情
// @Summary 获取组队活动详情
// @Description 根据ID获取组队活动详细信息
// @Tags team-activities
// @Accept json
// @Produce json
// @Param id path int true "活动ID"
// @Success 200 {object} model.TeamActivity
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities/{id} [get]
func (h *TeamActivityHandler) GetTeamActivity(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的活动ID"})
		return
	}

	activity, err := h.teamActivityService.GetTeamActivityByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取组队活动失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, activity)
}

// UpdateTeamActivity 更新组队活动
// @Summary 更新组队活动
// @Description 根据ID更新组队活动信息
// @Tags team-activities
// @Accept json
// @Produce json
// @Param id path int true "活动ID"
// @Param activity body model.TeamActivity true "更新的活动信息"
// @Success 200 {object} model.TeamActivity
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities/{id} [put]
func (h *TeamActivityHandler) UpdateTeamActivity(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的活动ID"})
		return
	}

	var activity model.TeamActivity
	if err := c.ShouldBindJSON(&activity); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}
	activity.ID = uint(id)

	if err := h.teamActivityService.UpdateTeamActivity(c.Request.Context(), &activity); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新组队活动失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, activity)
}

// DeleteTeamActivity 删除组队活动
// @Summary 删除组队活动
// @Description 根据ID删除组队活动
// @Tags team-activities
// @Accept json
// @Produce json
// @Param id path int true "活动ID"
// @Success 204 {object} nil
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities/{id} [delete]
func (h *TeamActivityHandler) DeleteTeamActivity(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的活动ID"})
		return
	}

	if err := h.teamActivityService.DeleteTeamActivity(c.Request.Context(), uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除组队活动失败: " + err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListTeamActivities 列出组队活动
// @Summary 分页列出组队活动
// @Description 分页获取组队活动列表
// @Tags team-activities
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页条数，默认10"
// @Success 200 {object} map[string]interface{}{"data":[]model.TeamActivity, "total":int64, "page":int, "pageSize":int}
// @Failure 500 {object} map[string]string
// @Router /api/team-activities [get]
func (h *TeamActivityHandler) ListTeamActivities(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	activities, total, err := h.teamActivityService.ListTeamActivities(c.Request.Context(), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取组队活动列表失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":     activities,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// JoinActivity 加入组队活动
// @Summary 加入组队活动
// @Description 用户加入指定的组队活动
// @Tags team-activities
// @Accept json
// @Produce json
// @Param activity_id path int true "活动ID"
// @Param participant body model.ActivityParticipant true "参与者信息"
// @Success 201 {object} model.ActivityParticipant
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities/{activity_id}/join [post]
func (h *TeamActivityHandler) JoinActivity(c *gin.Context) {
	activityIDStr := c.Param("activity_id")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的活动ID"})
		return
	}

	var participant model.ActivityParticipant
	if err := c.ShouldBindJSON(&participant); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}
	participant.TeamActivityID = uint(activityID)

	if err := h.teamActivityService.JoinActivity(c.Request.Context(), &participant); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, participant)
}

// LeaveActivity 退出组队活动
// @Summary 退出组队活动
// @Description 用户退出指定的组队活动
// @Tags team-activities
// @Accept json
// @Produce json
// @Param activity_id path int true "活动ID"
// @Param user_id query int true "用户ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities/{activity_id}/leave [post]
func (h *TeamActivityHandler) LeaveActivity(c *gin.Context) {
	activityIDStr := c.Param("activity_id")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的活动ID"})
		return
	}

	userIDStr := c.Query("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	if err := h.teamActivityService.LeaveActivity(c.Request.Context(), uint(activityID), uint(userID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "退出活动失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "成功退出活动"})
}

// GetActivityParticipants 获取活动参与者
// @Summary 获取活动参与者列表
// @Description 获取指定活动的所有参与者
// @Tags team-activities
// @Accept json
// @Produce json
// @Param activity_id path int true "活动ID"
// @Success 200 {object} []model.ActivityParticipant
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/team-activities/{activity_id}/participants [get]
func (h *TeamActivityHandler) GetActivityParticipants(c *gin.Context) {
	activityIDStr := c.Param("activity_id")
	activityID, err := strconv.ParseUint(activityIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的活动ID"})
		return
	}

	participants, err := h.teamActivityService.GetActivityParticipants(c.Request.Context(), uint(activityID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取参与者列表失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, participants)
}