package handler

import (
	"net/http"
	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/model"
	"github.com/chagee/go-backend/internal/service"
)

// DistrictHandler 地区控制器
type DistrictHandler struct {
	districtService service.DistrictService
}

// NewDistrictHandler 创建地区控制器实例
func NewDistrictHandler(districtService service.DistrictService) *DistrictHandler {
	return &DistrictHandler{
		districtService: districtService,
	}
}

// CreateDistrict 创建地区
// @Summary 创建新地区
// @Description 创建新地区并返回地区信息
// @Tags districts
// @Accept json
// @Produce json
// @Param district body model.District true "地区信息"
// @Success 201 {object} model.District
// @Failure 400 {object} gin.H{"error":string}
// @Failure 500 {object} gin.H{"error":string}
// @Router /districts [post]
func (h *DistrictHandler) CreateDistrict(c *gin.Context) {
	var district model.District
	if err := c.ShouldBindJSON(&district); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	if err := h.districtService.CreateDistrict(&district); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建地区失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, district)
}

// GetDistrictByAdcode 根据adcode获取地区
// @Summary 根据adcode获取地区
// @Description 根据adcode查询地区信息
// @Tags districts
// @Accept json
// @Produce json
// @Param adcode path string true "地区编码"
// @Success 200 {object} model.District
// @Failure 400 {object} gin.H{"error":string}
// @Failure 404 {object} gin.H{"error":string}
// @Failure 500 {object} gin.H{"error":string}
// @Router /districts/{adcode} [get]
func (h *DistrictHandler) GetDistrictByAdcode(c *gin.Context) {
	adcode := c.Param("adcode")
	if adcode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的地区编码"})
		return
	}

	district, err := h.districtService.GetDistrictByAdcode(adcode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取地区失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, district)
}

// ListDistricts 获取所有地区
// @Summary 获取所有地区
// @Description 获取系统中所有地区信息
// @Tags districts
// @Accept json
// @Produce json
// @Success 200 {array} model.District
// @Failure 500 {object} gin.H{"error":string}
// @Router /districts [get]
func (h *DistrictHandler) ListDistricts(c *gin.Context) {
	districts, err := h.districtService.ListDistricts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取地区列表失败: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, districts)
}

// ListDistrictsByParent 根据父级adcode获取子地区
// @Summary 根据父级adcode获取子地区
// @Description 根据父级adcode查询子地区信息
// @Tags districts
// @Accept json
// @Produce json
// @Param parentAdcode path string true "父级地区编码"
// @Success 200 {array} model.District
// @Failure 400 {object} gin.H{"error":string}
// @Failure 500 {object} gin.H{"error":string}
// @Router /districts/{parentAdcode}/children [get]
func (h *DistrictHandler) ListDistrictsByParent(c *gin.Context) {
	parentAdcode := c.Param("parentAdcode")
	if parentAdcode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的父级地区编码"})
		return
	}

	districts, err := h.districtService.ListDistrictsByParent(parentAdcode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取子地区列表失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, districts)
}

// UpdateDistrict 更新地区
// @Summary 更新地区信息
// @Description 根据adcode更新地区信息
// @Tags districts
// @Accept json
// @Produce json
// @Param adcode path string true "地区编码"
// @Param district body model.District true "地区信息"
// @Success 200 {object} model.District
// @Failure 400 {object} gin.H{"error":string}
// @Failure 404 {object} gin.H{"error":string}
// @Failure 500 {object} gin.H{"error":string}
// @Router /districts/{adcode} [put]
func (h *DistrictHandler) UpdateDistrict(c *gin.Context) {
	adcode := c.Param("adcode")
	if adcode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的地区编码"})
		return
	}

	var district model.District
	if err := c.ShouldBindJSON(&district); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	// 确保路径中的adcode与请求体中的一致
	district.Adcode = adcode

	if err := h.districtService.UpdateDistrict(&district); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新地区失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, district)
}

// DeleteDistrict 删除地区
// @Summary 删除地区
// @Description 根据adcode删除地区
// @Tags districts
// @Accept json
// @Produce json
// @Param adcode path string true "地区编码"
// @Success 204 {object} nil
// @Failure 400 {object} gin.H{"error":string}
// @Failure 500 {object} gin.H{"error":string}
// @Router /districts/{adcode} [delete]
func (h *DistrictHandler) DeleteDistrict(c *gin.Context) {
	adcode := c.Param("adcode")
	if adcode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的地区编码"})
		return
	}

	if err := h.districtService.DeleteDistrict(adcode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除地区失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}