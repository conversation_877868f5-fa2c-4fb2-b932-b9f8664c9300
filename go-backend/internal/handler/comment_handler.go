package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/model"
	"github.com/chagee/go-backend/internal/service"
)

// CommentHandler 评论API处理器
type CommentHandler struct {
	commentService service.CommentService
}

// NewCommentHandler 创建评论API处理器实例
func NewCommentHandler(commentService service.CommentService) *CommentHandler {
	return &CommentHandler{commentService: commentService}
}

// CreateComment 创建评论
// @Summary 创建新评论
// @Description 为帖子添加新评论
// @Tags comments
// @Accept json
// @Produce json
// @Param comment body model.Comment true "评论信息"
// @Success 201 {object} model.Comment
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/comments [post]
func (h *CommentHandler) CreateComment(c *gin.Context) {
	var comment model.Comment
	if err := c.ShouldBindJSON(&comment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	if err := h.commentService.CreateComment(c.Request.Context(), &comment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建评论失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, comment)
}

// GetComment 获取评论详情
// @Summary 获取评论详情
// @Description 根据ID获取评论详细信息
// @Tags comments
// @Accept json
// @Produce json
// @Param id path int true "评论ID"
// @Success 200 {object} model.Comment
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/comments/{id} [get]
func (h *CommentHandler) GetComment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的评论ID"})
		return
	}

	comment, err := h.commentService.GetCommentByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取评论失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, comment)
}

// UpdateComment 更新评论
// @Summary 更新评论
// @Description 根据ID更新评论内容
// @Tags comments
// @Accept json
// @Produce json
// @Param id path int true "评论ID"
// @Param comment body model.Comment true "更新的评论信息"
// @Success 200 {object} model.Comment
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/comments/{id} [put]
func (h *CommentHandler) UpdateComment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的评论ID"})
		return
	}

	var comment model.Comment
	if err := c.ShouldBindJSON(&comment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}
	comment.ID = uint(id)

	if err := h.commentService.UpdateComment(c.Request.Context(), &comment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新评论失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, comment)
}

// DeleteComment 删除评论
// @Summary 删除评论
// @Description 根据ID删除评论
// @Tags comments
// @Accept json
// @Produce json
// @Param id path int true "评论ID"
// @Success 204 {object} nil
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/comments/{id} [delete]
func (h *CommentHandler) DeleteComment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的评论ID"})
		return
	}

	if err := h.commentService.DeleteComment(c.Request.Context(), uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除评论失败: " + err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListCommentsByPostID 根据帖子ID列出评论
// @Summary 分页列出帖子评论
// @Description 根据帖子ID分页获取评论列表
// @Tags comments
// @Accept json
// @Produce json
// @Param post_id path int true "帖子ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页条数，默认10"
// @Success 200 {object} map[string]interface{}{"data":[]model.Comment, "total":int64, "page":int, "pageSize":int}
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/posts/{post_id}/comments [get]
func (h *CommentHandler) ListCommentsByPostID(c *gin.Context) {
	postIDStr := c.Param("post_id")
	postID, err := strconv.ParseUint(postIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的帖子ID"})
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	comments, total, err := h.commentService.ListCommentsByPostID(c.Request.Context(), uint(postID), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取评论列表失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":     comments,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}