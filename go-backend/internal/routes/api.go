package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/config"
	"github.com/chagee/go-backend/internal/handler"
	"github.com/chagee/go-backend/internal/service"
)

// SetupAPIRoutes 配置API路由
func SetupAPIRoutes(router *gin.Engine) {
	// 初始化服务
	postService := service.NewPostService(config.DB)
	teamActivityService := service.NewTeamActivityService(config.DB)
	commentService := service.NewCommentService(config.DB)

	// 初始化处理器
	postHandler := handler.NewPostHandler(postService)
	teamActivityHandler := handler.NewTeamActivityHandler(teamActivityService)
	commentHandler := handler.NewCommentHandler(commentService)

	// API路由组
	api := router.Group("/api")
	{
		// 帖子路由
		posts := api.Group("/posts")
		{
			posts.POST("", postHandler.CreatePost)
			posts.GET("/all", postHandler.ListPosts)

			// 帖子评论路由
			posts.GET("/:id/comments", commentHandler.ListCommentsByPostID)

			posts.GET("/:id", postHandler.GetPost)
			posts.PUT("/:id", postHandler.UpdatePost)
			posts.DELETE("/:id", postHandler.DeletePost)
		}

		// 评论路由
		comments := api.Group("/comments")
		{
			comments.POST("", commentHandler.CreateComment)
			comments.GET("/:id", commentHandler.GetComment)
			comments.PUT("/:id", commentHandler.UpdateComment)
			comments.DELETE("/:id", commentHandler.DeleteComment)
		}

		// 组队活动路由
		teamActivities := api.Group("/team-activities")
		{
			teamActivities.POST("", teamActivityHandler.CreateTeamActivity)
			teamActivities.GET("", teamActivityHandler.ListTeamActivities)

			// 活动参与路由
			teamActivities.POST("/participate/:aid/join", teamActivityHandler.JoinActivity)
			teamActivities.POST("/participate/:aid/leave", teamActivityHandler.LeaveActivity)
			teamActivities.GET("/participate/:aid/participants", teamActivityHandler.GetActivityParticipants)

			// 参数化路由放在最后
			teamActivities.GET("/:id", teamActivityHandler.GetTeamActivity)
			teamActivities.PUT("/:id", teamActivityHandler.UpdateTeamActivity)
			teamActivities.DELETE("/:id", teamActivityHandler.DeleteTeamActivity)
		}
	}
}