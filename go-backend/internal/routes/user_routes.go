package routes
import (
	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/config"
	"github.com/chagee/go-backend/internal/handler"
)

func RegisterUserRoutes(router *gin.RouterGroup, handler *handler.UserHandler) {
	cfg := config.Get()
	users := router.Group(cfg.Routes.Users.BasePath)
	{ 
		users.GET("", handler.ListUsers)
		users.POST(cfg.Routes.Users.Create, handler.CreateUser)
		users.GET(cfg.Routes.Users.GetByID, handler.GetUserByID)
		users.PUT(cfg.Routes.Users.GetByID, handler.UpdateUser)
		users.DELETE(cfg.Routes.Users.GetByID, handler.DeleteUser)
	}
}