package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/handler"
)

// RegisterDistrictRoutes 注册地区相关路由
func RegisterDistrictRoutes(router *gin.RouterGroup, handler *handler.DistrictHandler) {
	// 直接使用传入的router注册路由，避免路径重复
	router.POST("", handler.CreateDistrict)
	router.GET("", handler.ListDistricts)
	router.GET("/:adcode", handler.GetDistrictByAdcode)
	// 修改子地区路由路径以避免参数冲突
	router.GET("/children/:parentAdcode", handler.ListDistrictsByParent)
	router.PUT("/:adcode", handler.UpdateDistrict)
	router.DELETE("/:adcode", handler.DeleteDistrict)
}