package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/chagee/go-backend/internal/config"
	"github.com/chagee/go-backend/internal/handler"
	"github.com/chagee/go-backend/internal/service"
)

// RegisterAllRoutes 注册所有路由
func RegisterAllRoutes(router *gin.Engine) {
	api := router.Group(config.Get().Routes.ApiPrefix)
	{
		// 注册用户路由
		userService := service.NewUserService()
		userHandler := handler.NewUserHandler(userService)
		RegisterUserRoutes(api, userHandler)

		// 注册地区路由
		districtService := service.NewDistrictService()
		districtHandler := handler.NewDistrictHandler(districtService)
		RegisterDistrictRoutes(api, districtHandler)
	}
}

// SetupRoutes 路由设置
type SetupRoutes struct {
	api *gin.RouterGroup
}

// NewSetupRoutes 创建路由设置实例
func NewSetupRoutes(api *gin.RouterGroup) *SetupRoutes {
	return &SetupRoutes{api: api}
}

// RegisterAll 注册所有路由
func (s *SetupRoutes) RegisterAll() {
	s.RegisterUserRoutes()
	s.RegisterDistrictRoutes()
}

// RegisterUserRoutes 注册用户路由
func (s *SetupRoutes) RegisterUserRoutes() {
	userService := service.NewUserService()
	userHandler := handler.NewUserHandler(userService)
	// 硬编码用户路由基础路径，避免依赖未定义的配置项
	userGroup := s.api.Group("/users")
	RegisterUserRoutes(userGroup, userHandler)
}

// RegisterDistrictRoutes 注册地区路由
func (s *SetupRoutes) RegisterDistrictRoutes() {
	districtService := service.NewDistrictService()
	districtHandler := handler.NewDistrictHandler(districtService)
	// 创建地区路由组
	districtGroup := s.api.Group("/districts")
	RegisterDistrictRoutes(districtGroup, districtHandler)
}

// All 路由注册器
type All struct {
	api *gin.RouterGroup
}

// NewAll 创建路由注册器实例
func NewAll(api *gin.RouterGroup) *All {
	return &All{api: api}
}

// Register 注册所有路由
func (a *All) Register() {
	setup := NewSetupRoutes(a.api)
	setup.RegisterAll()
}