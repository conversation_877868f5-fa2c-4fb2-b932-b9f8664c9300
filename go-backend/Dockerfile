# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go mod files and download dependencies
COPY go.mod go.sum ./
RUN GOPROXY=https://goproxy.cn go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o app ./cmd/api

# Runtime stage
FROM golang:1.24-alpine AS runner

WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/app ./go-backend
RUN chmod +x ./go-backend
COPY --from=builder /app/configs ./configs

# Modify log configuration to output to stdout
RUN sed -i 's/filename: "logs\/app.log"/filename: "\/dev\/stdout"/' configs/config.yaml && \
    sed -i 's/tcp(localhost:3306)/tcp(host.docker.internal:3306)/' configs/config.yaml

# Create logs directory
RUN mkdir -p /app/logs && chmod 755 /app/logs

# Expose the application port
EXPOSE 8080

# Run the application
CMD ["sh", "-c", "cat configs/config.yaml && ./go-backend 2>&1; echo \"Application exited with code $?\""]