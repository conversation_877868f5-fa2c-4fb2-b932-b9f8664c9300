{"level":"info","msg":"服务器启动在端口 8080","time":"2025-06-16 18:39:29"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-16 18:40:16"}
{"level":"info","msg":"服务器启动在端口 8080","time":"2025-06-16 18:40:16"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-17 11:07:47"}
{"level":"info","msg":"服务器启动在端口 8080","time":"2025-06-17 11:07:47"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-17 15:07:08"}
{"level":"info","msg":"服务器启动在端口 8080","time":"2025-06-17 15:07:09"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 11:34:20"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 11:35:51"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 11:41:06"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 11:43:21"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 11:53:28"}
{"level":"info","msg":"服务器启动在端口 8080","time":"2025-06-20 11:53:28"}
{"level":"fatal","msg":"服务器启动失败: listen tcp :8080: bind: address already in use","time":"2025-06-20 11:53:28"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 11:57:50"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-20 11:57:50"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 13:48:16"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-20 13:48:16"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 13:56:40"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-20 13:56:40"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 14:00:14"}
{"level":"fatal","msg":"数据库初始化失败: 数据表迁移失败: Error 1062 (23000): Duplicate entry '150205' for key 'district.idx_district_adcode'","time":"2025-06-20 14:00:14"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 14:01:27"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-20 14:01:27"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 14:04:33"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-20 14:04:33"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-20 14:08:20"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-20 14:08:20"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:10:57"}
{"level":"fatal","msg":"数据库初始化失败: 数据表迁移失败: Error 1067 (42000): Invalid default value for 'joined_at'","time":"2025-06-23 16:10:57"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:12:06"}
{"level":"fatal","msg":"数据库初始化失败: 数据表迁移失败: Error 1067 (42000): Invalid default value for 'joined_at'","time":"2025-06-23 16:12:06"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:12:59"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:18:42"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:21:10"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:22:53"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:26:46"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:27:26"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:28:23"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:30:30"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:32:30"}
{"level":"info","msg":"服务器启动在端口 8081","time":"2025-06-23 16:32:30"}
{"level":"fatal","msg":"服务器启动失败: listen tcp :8081: bind: address already in use","time":"2025-06-23 16:32:30"}
{"level":"info","msg":"Logrus日志系统初始化成功","time":"2025-06-23 16:34:15"}
{"level":"info","msg":"服务器启动在端口 8082","time":"2025-06-23 16:34:15"}
